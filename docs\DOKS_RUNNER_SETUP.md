# DOKS Self-Hosted Runner Setup Guide

This guide explains how to configure your GitHub Actions self-hosted runners to work with DigitalOcean Kubernetes (DOKS) instead of minikube.

## Problem

Your GitHub Actions workflows are failing with "Kubernetes cluster is not accessible" because:
- Your local kubectl is configured for DOKS
- Your self-hosted runners are configured for minikube
- There's a configuration mismatch between local and CI/CD environments

## Solution Overview

Configure your self-hosted runners to use the same DOKS cluster as your local environment.

## Prerequisites

1. **DigitalOcean Access Token**
   - Go to [DigitalOcean API Tokens](https://cloud.digitalocean.com/account/api/tokens)
   - Click "Generate New Token"
   - Name: `GitOps-Runner`
   - Scopes: Read + Write
   - Copy the generated token

2. **GitHub Repository Secret**
   - Go to your repository settings
   - Navigate to "Secrets and variables" > "Actions"
   - Add new repository secret:
     - Name: `DIGITALOCEAN_ACCESS_TOKEN`
     - Value: Your DigitalOcean token

3. **Repository Variable**
   - In the same GitHub settings page
   - Navigate to "Variables" tab
   - Add new repository variable:
     - Name: `ENABLE_AUTO_DEPLOY`
     - Value: `true`

## Setup Instructions

### For Linux Self-Hosted Runners

1. **Run the setup script on your runner machine:**
   ```bash
   # Make sure DIGITALOCEAN_ACCESS_TOKEN is set
   export DIGITALOCEAN_ACCESS_TOKEN="your-token-here"
   
   # Run the setup script
   chmod +x scripts/setup-doks-runner.sh
   ./scripts/setup-doks-runner.sh
   ```

2. **Verify the setup:**
   ```bash
   kubectl cluster-info
   kubectl get namespace argocd
   ```

### For Windows Self-Hosted Runners

1. **Run PowerShell as Administrator**

2. **Set the access token:**
   ```powershell
   $env:DIGITALOCEAN_ACCESS_TOKEN = "your-token-here"
   ```

3. **Run the setup script:**
   ```powershell
   .\scripts\setup-doks-runner.ps1
   ```

## What the Setup Scripts Do

1. **Install Required Tools:**
   - `doctl` (DigitalOcean CLI)
   - `kubectl` (Kubernetes CLI)

2. **Configure Authentication:**
   - Authenticate with DigitalOcean using your access token
   - Download and configure DOKS cluster kubeconfig

3. **Verify Connectivity:**
   - Test connection to DOKS cluster
   - Verify ArgoCD namespace exists
   - Check ArgoCD accessibility

## Updated Workflow Features

The updated `.github/workflows/deploy-from-cicd.yaml` now includes:

1. **DOKS Authentication:**
   - Uses `DIGITALOCEAN_ACCESS_TOKEN` secret
   - Installs and configures `doctl`
   - Downloads DOKS cluster configuration

2. **Improved Error Handling:**
   - Better error messages for missing tokens
   - Clear troubleshooting instructions
   - Graceful fallback when auto-deploy is disabled

3. **Cluster Verification:**
   - Tests DOKS connectivity before deployment
   - Verifies ArgoCD installation
   - Provides detailed status information

## Troubleshooting

### "DIGITALOCEAN_ACCESS_TOKEN is not set"
- Ensure you've added the token as a repository secret
- Check that the secret name is exactly `DIGITALOCEAN_ACCESS_TOKEN`
- Verify the token has Read + Write permissions

### "Failed to connect to DOKS cluster"
- Check your DigitalOcean token permissions
- Verify the cluster ID in the script matches your cluster
- Ensure your DOKS cluster is running

### "ArgoCD namespace not found"
- Install ArgoCD on your DOKS cluster:
  ```bash
  kubectl create namespace argocd
  kubectl apply -n argocd -f https://raw.githubusercontent.com/argoproj/argo-cd/stable/manifests/install.yaml
  ```

### "Auto-deployment is disabled"
- Set the `ENABLE_AUTO_DEPLOY` repository variable to `true`
- Go to repository settings > Secrets and variables > Actions > Variables

## Cluster Information

Your current DOKS cluster:
- **Cluster ID:** `158b6a47-3e7e-4dca-af0f-05a6e07115af`
- **Cluster Name:** `do-blr1-k8s-1-33-1-do-1-blr1-1752649393416`
- **API Server:** `https://158b6a47-3e7e-4dca-af0f-05a6e07115af.k8s.ondigitalocean.com`
- **Region:** `blr1` (Bangalore)

## Testing the Setup

After configuration, test your setup:

1. **Trigger a deployment:**
   - Create a repository dispatch event
   - Or push to a branch that triggers your CI/CD

2. **Monitor the workflow:**
   - Check GitHub Actions logs
   - Look for successful DOKS authentication
   - Verify ArgoCD deployment steps

3. **Verify in ArgoCD:**
   - Access your ArgoCD dashboard
   - Check for new applications
   - Monitor sync status

## Next Steps

1. **Update all self-hosted runners** with the DOKS configuration
2. **Test the complete CI/CD pipeline** with a sample application
3. **Monitor ArgoCD dashboard** for deployment status
4. **Set up ArgoCD UI access** if not already configured

## Support

If you encounter issues:
1. Check the GitHub Actions workflow logs
2. Verify DOKS cluster status in DigitalOcean console
3. Test kubectl connectivity manually on runner machines
4. Review ArgoCD logs if deployment fails
