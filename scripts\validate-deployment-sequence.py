#!/usr/bin/env python3
"""
Deployment Sequence Validation Script
Validates that deployments follow the correct sequence: dev → staging → production
"""

import argparse
import json
import sys
from pathlib import Path


def print_status(message, status_type="INFO"):
    """Print colored status messages"""
    colors = {
        "SUCCESS": "\033[32m[SUCCESS]",
        "ERROR": "\033[31m[ERROR]",
        "WARNING": "\033[33m[WARNING]",
        "INFO": "\033[36m[INFO]"
    }
    reset = "\033[0m"
    color = colors.get(status_type, "")
    print(f"{color} {message}{reset}")


def check_deployment_exists(project_id, environment):
    """Check if a deployment exists for the given project and environment"""
    deployment_path = Path(f"deployments/{project_id}/overlays/{environment}")
    argocd_app_path = Path(f"deployments/{project_id}/overlays/{environment}/application.yaml")
    
    if not deployment_path.exists():
        return False, f"Deployment directory not found: {deployment_path}"
    
    if not argocd_app_path.exists():
        return False, f"ArgoCD application not found: {argocd_app_path}"
    
    return True, "Deployment exists"


def validate_deployment_sequence(project_id, target_environment):
    """Validate that the deployment sequence is followed"""
    print_status(f"Validating deployment sequence for {project_id} → {target_environment}", "INFO")
    
    # Define the required sequence
    sequence = ["dev", "staging", "production"]
    target_index = sequence.index(target_environment)
    
    # Check all previous environments exist
    for i in range(target_index):
        env = sequence[i]
        exists, message = check_deployment_exists(project_id, env)
        
        if not exists:
            print_status(f"Missing prerequisite deployment: {env}", "ERROR")
            print_status(f"  {message}", "ERROR")
            print_status(f"  Cannot deploy to {target_environment} without {env}", "ERROR")
            return False
        else:
            print_status(f"✓ Prerequisite deployment exists: {env}", "SUCCESS")
    
    # Check if target environment deployment exists
    exists, message = check_deployment_exists(project_id, target_environment)
    if exists:
        print_status(f"✓ Target deployment exists: {target_environment}", "SUCCESS")
    else:
        print_status(f"Target deployment will be created: {target_environment}", "INFO")
    
    return True


def validate_docker_tags(payload):
    """Validate that Docker tags follow the environment-specific pattern"""
    environment = payload.get('environment')
    docker_tag = payload.get('docker_tag')
    
    expected_tags = {
        'dev': 'latest',
        'staging': 'staging', 
        'production': 'production'
    }
    
    expected_tag = expected_tags.get(environment)
    if not expected_tag:
        print_status(f"Unknown environment: {environment}", "ERROR")
        return False
    
    if docker_tag != expected_tag:
        print_status(f"Docker tag mismatch for {environment}", "WARNING")
        print_status(f"  Expected: {expected_tag}", "WARNING")
        print_status(f"  Actual: {docker_tag}", "WARNING")
        print_status(f"  This may indicate a deployment sequence issue", "WARNING")
        return False
    
    print_status(f"✓ Docker tag correct for {environment}: {docker_tag}", "SUCCESS")
    return True


def main():
    parser = argparse.ArgumentParser(description="Validate GitOps deployment sequence")
    parser.add_argument("--project-id", required=True, help="Project ID to validate")
    parser.add_argument("--environment", required=True, 
                       choices=["dev", "staging", "production"], 
                       help="Target environment")
    parser.add_argument("--payload", help="GitHub Actions payload JSON (optional)")
    parser.add_argument("--strict", action="store_true", 
                       help="Fail validation if sequence is not followed")
    
    args = parser.parse_args()
    
    print("GitOps Deployment Sequence Validation")
    print("=" * 40)
    
    # Validate deployment sequence
    sequence_valid = validate_deployment_sequence(args.project_id, args.environment)
    
    # Validate payload if provided
    payload_valid = True
    if args.payload:
        try:
            payload = json.loads(args.payload)
            payload_valid = validate_docker_tags(payload)
        except json.JSONDecodeError as e:
            print_status(f"Invalid payload JSON: {e}", "ERROR")
            payload_valid = False
    
    # Summary
    print("\n" + "=" * 40)
    if sequence_valid and payload_valid:
        print_status("✅ Deployment sequence validation passed", "SUCCESS")
        
        # Show deployment flow
        print("\n📋 Deployment Flow:")
        environments = ["dev", "staging", "production"]
        current_index = environments.index(args.environment)
        
        for i, env in enumerate(environments):
            if i < current_index:
                print(f"  ✅ {env} (completed)")
            elif i == current_index:
                print(f"  🎯 {env} (current target)")
            else:
                print(f"  ⏳ {env} (pending)")
        
        print(f"\n🔗 Next Steps:")
        if args.environment == "dev":
            print("  • Monitor dev deployment")
            print("  • Test application functionality")
            print("  • Approve staging promotion when ready")
        elif args.environment == "staging":
            print("  • Monitor staging deployment")
            print("  • Run staging tests")
            print("  • Approve production promotion when ready")
        elif args.environment == "production":
            print("  • Monitor production deployment")
            print("  • Verify application health")
            print("  • Run production smoke tests")
        
        sys.exit(0)
    else:
        print_status("❌ Deployment sequence validation failed", "ERROR")
        
        if not sequence_valid:
            print_status("  Deployment sequence issues detected", "ERROR")
        if not payload_valid:
            print_status("  Payload validation issues detected", "ERROR")
        
        if args.strict:
            print_status("Strict mode enabled - failing validation", "ERROR")
            sys.exit(1)
        else:
            print_status("Non-strict mode - continuing with warnings", "WARNING")
            sys.exit(0)


if __name__ == "__main__":
    main()
