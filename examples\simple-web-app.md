# Example: Simple Web Application Deployment

This example demonstrates deploying a simple Nginx-based web application using the self-service GitOps system.

## Application Details
- **Type**: Static web application
- **Technology**: Nginx
- **Environment**: Development
- **External Access**: Yes (via Ingress)

## Issue Template Values

When creating the deployment issue, use these values:

```yaml
Application Name: Frontend Dashboard
Project Identifier: frontend-dashboard
Container Image: nginx:1.21
Environment: dev
Kubernetes Namespace: frontend-dashboard
Replica Count: 2
CPU Request: 100m
CPU Limit: 200m
Memory Request: 128Mi
Memory Limit: 256Mi
Container Port: 80
Service Type: NodePort
NodePort: 30080
Enable Ingress: ✓
Ingress Host: dashboard.example.com
Ingress Path: /
Application Type: web-app
Health Check Path: /
```

## Generated Structure

After submitting the issue, the following files will be generated:

```
frontend-dashboard/
├── argocd/
│   ├── application.yaml
│   └── project.yaml
└── k8s/
    ├── namespace.yaml
    ├── deployment.yaml
    ├── service.yaml
    └── ingress.yaml
```

## Deployment Steps

1. **Create the Issue**: Use the values above in the deployment issue template
2. **Wait for Generation**: The automation will create all manifests
3. **Deploy with ArgoCD**:
   ```bash
   kubectl apply -f frontend-dashboard/argocd/project.yaml
   kubectl apply -f frontend-dashboard/argocd/application.yaml
   ```
4. **Verify Deployment**:
   ```bash
   kubectl get pods -n frontend-dashboard
   kubectl get svc -n frontend-dashboard
   kubectl get ingress -n frontend-dashboard
   ```

## Access the Application

### Via NodePort
```bash
# Get cluster IP
minikube ip

# Access application
curl http://{cluster-ip}:30080
```

### Via Ingress (if configured)
```bash
# Add to /etc/hosts (for local testing)
echo "{cluster-ip} dashboard.example.com" >> /etc/hosts

# Access via domain
curl http://dashboard.example.com
```

## Customization Options

### Custom HTML Content
To serve custom content, build a custom image:

```dockerfile
FROM nginx:1.21
COPY ./html /usr/share/nginx/html
EXPOSE 80
```

### Environment-Specific Configuration
For different environments, create separate deployments:
- `frontend-dashboard-dev`
- `frontend-dashboard-staging`
- `frontend-dashboard-prod`

### SSL/TLS Configuration
Add TLS configuration to the ingress:

```yaml
# Add to ingress.yaml after generation
spec:
  tls:
  - hosts:
    - dashboard.example.com
    secretName: dashboard-tls
```

## Monitoring

### Health Checks
The deployment includes health checks on the root path (`/`). Nginx returns 200 OK for the default page.

### Resource Usage
Monitor resource usage:
```bash
kubectl top pods -n frontend-dashboard
```

### Logs
View application logs:
```bash
kubectl logs deployment/frontend-dashboard -n frontend-dashboard
```

## Troubleshooting

### Pod Not Starting
```bash
# Check pod status
kubectl describe pod -l app=frontend-dashboard -n frontend-dashboard

# Check events
kubectl get events -n frontend-dashboard --sort-by='.lastTimestamp'
```

### Ingress Not Working
```bash
# Check ingress status
kubectl describe ingress frontend-dashboard-ingress -n frontend-dashboard

# Verify ingress controller
kubectl get pods -n ingress-nginx
```

### Service Not Accessible
```bash
# Test service connectivity
kubectl port-forward svc/frontend-dashboard-service 8080:80 -n frontend-dashboard

# Access via port-forward
curl http://localhost:8080
```
