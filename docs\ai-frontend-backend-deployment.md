# AI React Frontend & Spring Backend Cross-Namespace Deployment Guide

This guide provides comprehensive instructions for deploying the AI React Frontend and AI Spring Backend applications with proper cross-namespace communication in Kubernetes.

## Overview

The deployment consists of:
- **AI React Frontend**: React application running on port 3000 in `ai-react-frontend-dev` namespace
- **AI Spring Backend**: Spring Boot application running on port 8080 in `ai-spring-backend-dev` namespace
- **Cross-namespace communication**: Enabled via NetworkPolicies and internal DNS
- **External access**: Both services exposed via LoadBalancer

## Prerequisites

- Kubernetes cluster with ArgoCD installed
- kubectl configured to access the cluster
- Network policy support enabled in the cluster
- LoadBalancer support (cloud provider or MetalLB)

## Deployment Order

### 1. Deploy Namespaces First

```bash
# Create namespaces with proper labels
kubectl apply -f ai-react-frontend/k8s/namespace.yaml
kubectl apply -f ai-spring-backend/k8s/namespace.yaml

# Verify namespace creation
kubectl get namespaces -l cross-namespace-communication=enabled
```

### 2. Deploy Backend Application (Spring Boot)

```bash
# Deploy backend components in order
kubectl apply -f ai-spring-backend/k8s/secret.yaml
kubectl apply -f ai-spring-backend/k8s/configmap.yaml
kubectl apply -f ai-spring-backend/k8s/postgres-pvc.yaml
kubectl apply -f ai-spring-backend/k8s/postgres-deployment.yaml
kubectl apply -f ai-spring-backend/k8s/postgres-service.yaml
kubectl apply -f ai-spring-backend/k8s/deployment.yaml
kubectl apply -f ai-spring-backend/k8s/service.yaml
kubectl apply -f ai-spring-backend/k8s/networkpolicy.yaml

# Wait for backend to be ready
kubectl wait --for=condition=ready pod -l app=ai-spring-backend -n ai-spring-backend-dev --timeout=300s
```

### 3. Deploy Frontend Application (React)

```bash
# Deploy frontend components
kubectl apply -f ai-react-frontend/k8s/secret.yaml
kubectl apply -f ai-react-frontend/k8s/configmap.yaml
kubectl apply -f ai-react-frontend/k8s/deployment.yaml
kubectl apply -f ai-react-frontend/k8s/service.yaml
kubectl apply -f ai-react-frontend/k8s/networkpolicy.yaml

# Wait for frontend to be ready
kubectl wait --for=condition=ready pod -l app=ai-react-frontend -n ai-react-frontend-dev --timeout=300s
```

### 4. Deploy ArgoCD Applications (GitOps)

```bash
# Deploy ArgoCD projects and applications
kubectl apply -f ai-spring-backend/argocd/project.yaml
kubectl apply -f ai-spring-backend/argocd/application.yaml
kubectl apply -f ai-react-frontend/argocd/project.yaml
kubectl apply -f ai-react-frontend/argocd/application.yaml

# Monitor ArgoCD sync status
kubectl get applications -n argocd
```

## Verification Commands

### Check Application Status

```bash
# Check all pods
kubectl get pods -n ai-react-frontend-dev
kubectl get pods -n ai-spring-backend-dev

# Check services and external IPs
kubectl get services -n ai-react-frontend-dev
kubectl get services -n ai-spring-backend-dev

# Check ArgoCD application status
kubectl get applications -n argocd -l app.kubernetes.io/part-of=ai-react-frontend
kubectl get applications -n argocd -l app.kubernetes.io/part-of=ai-spring-backend
```

### Test Cross-Namespace Communication

```bash
# Test internal DNS resolution from frontend to backend
kubectl exec -n ai-react-frontend-dev deployment/ai-react-frontend -- \
  nslookup ai-spring-backend-service.ai-spring-backend-dev.svc.cluster.local

# Test HTTP connectivity from frontend to backend
kubectl exec -n ai-react-frontend-dev deployment/ai-react-frontend -- \
  curl -I http://ai-spring-backend-service.ai-spring-backend-dev.svc.cluster.local:8080/actuator/health

# Test backend health endpoint
kubectl exec -n ai-spring-backend-dev deployment/ai-spring-backend -- \
  curl -I http://localhost:8080/actuator/health
```

### Check Network Policies

```bash
# Verify network policies are applied
kubectl get networkpolicies -n ai-react-frontend-dev
kubectl get networkpolicies -n ai-spring-backend-dev

# Describe network policies for details
kubectl describe networkpolicy ai-react-frontend-network-policy -n ai-react-frontend-dev
kubectl describe networkpolicy ai-spring-backend-network-policy -n ai-spring-backend-dev
```

## External Access

### Get LoadBalancer IPs

```bash
# Get frontend external IP
FRONTEND_IP=$(kubectl get service ai-react-frontend-service -n ai-react-frontend-dev -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
echo "Frontend URL: http://$FRONTEND_IP:3000"

# Get backend external IP
BACKEND_IP=$(kubectl get service ai-spring-backend-service -n ai-spring-backend-dev -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
echo "Backend URL: http://$BACKEND_IP:8080"
```

### Test External Access

```bash
# Test frontend accessibility
curl -I http://$FRONTEND_IP:3000

# Test backend health endpoint
curl -I http://$BACKEND_IP:8080/actuator/health

# Test CORS configuration
curl -H "Origin: http://$FRONTEND_IP:3000" \
     -H "Access-Control-Request-Method: GET" \
     -H "Access-Control-Request-Headers: Content-Type" \
     -X OPTIONS \
     http://$BACKEND_IP:8080/api/test
```

## Configuration Details

### Internal Service URLs

The applications use the following internal DNS names for communication:

- **Frontend to Backend**: `ai-spring-backend-service.ai-spring-backend-dev.svc.cluster.local:8080`
- **Backend to Frontend**: `ai-react-frontend-service.ai-react-frontend-dev.svc.cluster.local:3000`

### Environment Variables

#### Frontend ConfigMap
- `REACT_APP_API_URL`: Points to backend internal service
- `REACT_APP_BACKEND_SERVICE_URL`: Full internal DNS name
- `REACT_APP_OAUTH_REDIRECT_URI`: OAuth callback URL

#### Backend ConfigMap
- `FRONTEND_SERVICE_URL`: Points to frontend internal service
- `CORS_ALLOWED_ORIGINS`: Includes frontend internal and external URLs
- `OAUTH_REDIRECT_URI_INTERNAL`: Internal OAuth redirect

## Troubleshooting

### Common Issues

#### 1. Pods Not Starting
```bash
# Check pod events
kubectl describe pod -l app=ai-react-frontend -n ai-react-frontend-dev
kubectl describe pod -l app=ai-spring-backend -n ai-spring-backend-dev

# Check logs
kubectl logs -l app=ai-react-frontend -n ai-react-frontend-dev
kubectl logs -l app=ai-spring-backend -n ai-spring-backend-dev
```

#### 2. Network Connectivity Issues
```bash
# Check if network policies are blocking traffic
kubectl get networkpolicies --all-namespaces

# Test without network policies (temporarily)
kubectl delete networkpolicy --all -n ai-react-frontend-dev
kubectl delete networkpolicy --all -n ai-spring-backend-dev
```

#### 3. LoadBalancer Not Getting External IP
```bash
# Check LoadBalancer service status
kubectl describe service ai-react-frontend-service -n ai-react-frontend-dev
kubectl describe service ai-spring-backend-service -n ai-spring-backend-dev

# Check cloud provider LoadBalancer support
kubectl get nodes -o wide
```

#### 4. CORS Issues
```bash
# Check CORS configuration in backend
kubectl get configmap ai-spring-backend-cors-config -n ai-spring-backend-dev -o yaml

# Test CORS headers
curl -H "Origin: http://localhost:3000" \
     -v http://$BACKEND_IP:8080/api/test
```

### Debug Commands

```bash
# Port forward for local testing
kubectl port-forward -n ai-react-frontend-dev service/ai-react-frontend-service 3000:3000 &
kubectl port-forward -n ai-spring-backend-dev service/ai-spring-backend-service 8080:8080 &

# Check DNS resolution
kubectl run -it --rm debug --image=busybox --restart=Never -- nslookup ai-spring-backend-service.ai-spring-backend-dev.svc.cluster.local

# Network connectivity test
kubectl run -it --rm netshoot --image=nicolaka/netshoot --restart=Never -- /bin/bash
```

## Cleanup

```bash
# Delete applications
kubectl delete -f ai-react-frontend/k8s/
kubectl delete -f ai-spring-backend/k8s/

# Delete ArgoCD applications
kubectl delete -f ai-react-frontend/argocd/
kubectl delete -f ai-spring-backend/argocd/

# Delete namespaces (this will delete all resources in the namespaces)
kubectl delete namespace ai-react-frontend-dev
kubectl delete namespace ai-spring-backend-dev
```

## Security Considerations

1. **Network Policies**: Restrict traffic to only necessary communication paths
2. **Service Accounts**: Use dedicated service accounts with minimal permissions
3. **Secrets**: Store sensitive data in Kubernetes secrets, not ConfigMaps
4. **RBAC**: Implement proper role-based access control
5. **Pod Security**: Use security contexts and pod security standards

## Monitoring

```bash
# Monitor resource usage
kubectl top pods -n ai-react-frontend-dev
kubectl top pods -n ai-spring-backend-dev

# Check application metrics (if Prometheus is available)
kubectl get servicemonitor --all-namespaces
```
