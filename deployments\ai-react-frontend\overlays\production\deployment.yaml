apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-react-frontend
  labels:
    app: ai-react-frontend
    app.kubernetes.io/name: ai-react-frontend
    app.kubernetes.io/component: react-frontend
    app.kubernetes.io/part-of: AI React Frontend
    app.kubernetes.io/version: "405e961c"
    app.kubernetes.io/managed-by: argocd
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ai-react-frontend
  template:
    metadata:
      labels:
        app: ai-react-frontend
        app.kubernetes.io/name: ai-react-frontend
        app.kubernetes.io/component: react-frontend
        app.kubernetes.io/part-of: AI React Frontend
        app.kubernetes.io/version: "405e961c"
    spec:
      containers:
      - name: ai-react-frontend
        image: registry.digitalocean.com/doks-registry/ai-react-frontend:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 3000
          name: http
        envFrom:
        - configMapRef:
            name: ai-react-frontend-config
        env:
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: ai-react-frontend-secrets
              key: JWT_SECRET
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: ai-react-frontend-secrets
              key: DB_USER
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: ai-react-frontend-secrets
              key: DB_PASSWORD
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: ai-react-frontend-secrets
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            secretKeyRef:
              name: ai-react-frontend-secrets
              key: DB_PORT
        - name: DB_NAME
          valueFrom:
            secretKeyRef:
              name: ai-react-frontend-secrets
              key: DB_NAME
        - name: DB_SSL_MODE
          valueFrom:
            secretKeyRef:
              name: ai-react-frontend-secrets
              key: DB_SSL_MODE
        
        
        
        - name: SMTP_USER
          valueFrom:
            secretKeyRef:
              name: ai-react-frontend-secrets
              key: SMTP_USER
        - name: SMTP_PASS
          valueFrom:
            secretKeyRef:
              name: ai-react-frontend-secrets
              key: SMTP_PASS
        - name: GOOGLE_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: ai-react-frontend-secrets
              key: GOOGLE_CLIENT_ID
        - name: GOOGLE_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: ai-react-frontend-secrets
              key: GOOGLE_CLIENT_SECRET
        
        
        
        
        # React Frontend Health Checks (TCP for static content)
        livenessProbe:
          tcpSocket:
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          tcpSocket:
            port: 3000
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 3
          failureThreshold: 3
        
        resources:
          requests:
            memory: "4Gi"
            cpu: "2000m"
          limits:
            memory: "8Gi"
            cpu: "4000m"
