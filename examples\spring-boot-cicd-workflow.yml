# Spring Boot CI/CD Workflow with Managed Database Secrets
# This workflow demonstrates how to properly encode managed DigitalOcean database secrets
# and dispatch them to the GitOps automation system for Spring Boot applications

name: 🚀 Spring Boot CI/CD with GitOps

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

env:
  REGISTRY: registry.digitalocean.com/doks-registry
  IMAGE_NAME: ai-spring-backend-saipriya

jobs:
  build-and-test:
    runs-on: ubuntu-latest
    steps:
      - name: 📥 Checkout Code
        uses: actions/checkout@v4

      - name: ☕ Set up JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'temurin'

      - name: 📦 Cache Maven Dependencies
        uses: actions/cache@v3
        with:
          path: ~/.m2
          key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
          restore-keys: ${{ runner.os }}-m2

      - name: 🔧 Build with Maven
        run: mvn clean compile

      - name: 🧪 Run Tests
        run: mvn test

      - name: 📊 Generate Test Report
        uses: dorny/test-reporter@v1
        if: success() || failure()
        with:
          name: <PERSON>ven Tests
          path: target/surefire-reports/*.xml
          reporter: java-junit

  build-and-push-docker:
    needs: build-and-test
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    outputs:
      image-tag: ${{ steps.docker-build.outputs.image-tag }}
      docker-image: ${{ steps.docker-build.outputs.docker-image }}
    steps:
      - name: 📥 Checkout Code
        uses: actions/checkout@v4

      - name: ☕ Set up JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'temurin'

      - name: 📦 Build JAR
        run: mvn clean package -DskipTests

      - name: 🐳 Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 🔐 Log in to DigitalOcean Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}
          password: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}

      - name: 🏗️ Build and Push Docker Image
        id: docker-build
        run: |
          # Generate image tag
          IMAGE_TAG="v$(date +%Y%m%d)-${GITHUB_SHA:0:7}"
          FULL_IMAGE_NAME="${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}"
          
          # Build and push Docker image
          docker build -t "${FULL_IMAGE_NAME}:${IMAGE_TAG}" -t "${FULL_IMAGE_NAME}:latest" .
          docker push "${FULL_IMAGE_NAME}:${IMAGE_TAG}"
          docker push "${FULL_IMAGE_NAME}:latest"
          
          # Set outputs
          echo "image-tag=${IMAGE_TAG}" >> $GITHUB_OUTPUT
          echo "docker-image=${FULL_IMAGE_NAME}" >> $GITHUB_OUTPUT
          
          echo "✅ Docker image built and pushed: ${FULL_IMAGE_NAME}:${IMAGE_TAG}"

  deploy-to-gitops:
    needs: build-and-push-docker
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    runs-on: ubuntu-latest
    steps:
      - name: 🔐 Create Managed Database Secrets Payload
        id: create-secrets
        run: |
          # Create secrets JSON with managed DigitalOcean database configuration
          SECRETS_JSON=$(cat << EOF | base64 -w 0
          {
            "JWT_SECRET": "${{ secrets.JWT_SECRET || 'supersecretkey' }}",
            "DB_USER": "${{ secrets.DB_USER || 'spring_dev_user' }}",
            "DB_PASSWORD": "${{ secrets.DB_PASSWORD || 'AVNS_0bYzt0GZdky7rnP8Kl7' }}",
            "DB_HOST": "${{ secrets.DB_HOST || 'private-dbaas-db-10329328-do-user-23815742-0.m.db.ondigitalocean.com' }}",
            "DB_PORT": "${{ secrets.DB_PORT || '25060' }}",
            "DB_NAME": "${{ secrets.DB_NAME || 'spring_dev_db' }}",
            "DB_SSL_MODE": "${{ secrets.DB_SSL_MODE || 'require' }}",
            "SMTP_USER": "${{ secrets.SMTP_USER || '<EMAIL>' }}",
            "SMTP_PASS": "${{ secrets.SMTP_PASS || 'fqactehafmzlltzz' }}",
            "GOOGLE_CLIENT_ID": "${{ secrets.GOOGLE_CLIENT_ID || '1073981864538-3uiik72ohsfr2ouioror3fm1jqc493os.apps.googleusercontent.com' }}",
            "GOOGLE_CLIENT_SECRET": "${{ secrets.GOOGLE_CLIENT_SECRET || '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' }}"
          }
          EOF
          )
          
          echo "secrets-encoded=${SECRETS_JSON}" >> $GITHUB_OUTPUT
          echo "✅ Secrets payload created and base64 encoded"

      - name: 🚀 Trigger GitOps Deployment
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITOPS_DEPLOY_TOKEN }}
          script: |
            // Check if this is a merge commit (has multiple parents)
            const commit = await github.rest.git.getCommit({
              owner: context.repo.owner,
              repo: context.repo.repo,
              commit_sha: context.sha
            });
            
            const isMergeCommit = commit.data.parents.length > 1;
            
            if (!isMergeCommit) {
              console.log('Not a merge commit, skipping GitOps deployment');
              return;
            }
            
            console.log('Merge commit detected, triggering GitOps deployment...');
            
            // Determine environment based on branch
            const environment = 'dev'; // Deploy to dev environment on main branch merges
            
            // Get Docker image details
            const dockerImage = '${{ needs.build-and-push-docker.outputs.docker-image }}';
            const dockerTag = '${{ needs.build-and-push-docker.outputs.image-tag }}';
            const secretsEncoded = '${{ steps.create-secrets.outputs.secrets-encoded }}';
            
            // Create payload matching the specified format
            const payload = {
              project_id: 'ai-spring-backend-saipriya',
              application_type: 'springboot-backend',
              environment: environment,
              docker_image: dockerImage,
              docker_tag: dockerTag,
              source_repo: `${context.repo.owner}/${context.repo.repo}`,
              source_branch: '${{ github.ref_name }}',
              commit_sha: context.sha,
              secrets_encoded: secretsEncoded || ''
            };
            
            console.log('Payload:', JSON.stringify(payload, null, 2));
            
            // Trigger GitOps deployment
            await github.rest.repos.createDispatchEvent({
              owner: 'ChidhagniConsulting',
              repo: 'gitops-argocd-apps', 
              event_type: 'deploy-to-argocd',
              client_payload: payload
            });
            
            console.log(`✅ GitOps deployment triggered for ai-spring-backend-saipriya`);
            console.log(`🐳 Docker image: ${dockerImage}:${dockerTag}`);
            console.log(`🌍 Environment: ${environment}`);
            console.log(`🔐 Secrets: ${secretsEncoded ? 'Provided' : 'Using defaults'}`);

  notify-deployment:
    needs: [build-and-push-docker, deploy-to-gitops]
    if: always() && github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
      - name: 📢 Deployment Notification
        run: |
          if [ "${{ needs.deploy-to-gitops.result }}" == "success" ]; then
            echo "✅ Deployment pipeline completed successfully!"
            echo "🐳 Image: ${{ needs.build-and-push-docker.outputs.docker-image }}:${{ needs.build-and-push-docker.outputs.image-tag }}"
            echo "🚀 GitOps deployment triggered for ai-spring-backend-saipriya"
          else
            echo "❌ Deployment pipeline failed!"
            echo "Please check the logs for more details."
          fi
