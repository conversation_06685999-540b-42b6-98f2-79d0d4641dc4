apiVersion: v1
kind: Secret
metadata:
  name: ai-react-frontend-secrets
  labels:
    app: ai-react-frontend
    app.kubernetes.io/name: ai-react-frontend
    app.kubernetes.io/component: secrets
    app.kubernetes.io/part-of: AI React Frontend
    app.kubernetes.io/version: "405e961c"
    app.kubernetes.io/managed-by: argocd
    environment: production
type: Opaque
data:
  # Production Environment Secrets
  # These placeholders will be replaced with base64-encoded values from secrets_encoded
  SPRING_DATASOURCE_URL: DYNAMIC_SPRING_DATASOURCE_URL_B64
  
  # Essential Authentication Secrets
  JWT_SECRET: DYNAMIC_JWT_SECRET_B64
  
  # Database Credentials (Production)
  DB_USER: DYNAMIC_DB_USER_B64
  DB_PASSWORD: DYNAMIC_DB_PASSWORD_B64
  DB_HOST: DYNAMIC_DB_HOST_B64
  DB_PORT: DYNAMIC_DB_PORT_B64
  DB_NAME: DYNAMIC_DB_NAME_B64
  DB_SSL_MODE: DYNAMIC_DB_SSL_MODE_B64
  
  # SMTP Configuration (Production)
  SMTP_USER: DYNAMIC_SMTP_USER_B64
  SMTP_PASS: DYNAMIC_SMTP_PASS_B64
  
  # OAuth2 Configuration (Production)
  GOOGLE_CLIENT_ID: DYNAMIC_GOOGLE_CLIENT_ID_B64
  GOOGLE_CLIENT_SECRET: DYNAMIC_GOOGLE_CLIENT_SECRET_B64
  
  # Production-specific secrets
  # MONITORING_API_KEY: DYNAMIC_MONITORING_API_KEY_B64
  # EXTERNAL_API_KEY: DYNAMIC_EXTERNAL_API_KEY_B64
  # BACKUP_ENCRYPTION_KEY: DYNAMIC_BACKUP_ENCRYPTION_KEY_B64
  # SECURITY_TOKEN: DYNAMIC_SECURITY_TOKEN_B64
