#!/bin/bash

# GitHub Environment Verification Script
# This script helps verify that GitHub environments are properly configured for GitOps approvals

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "SUCCESS")
            echo -e "${GREEN}✅ $message${NC}"
            ;;
        "ERROR")
            echo -e "${RED}❌ $message${NC}"
            ;;
        "WARNING")
            echo -e "${YELLOW}⚠️  $message${NC}"
            ;;
        "INFO")
            echo -e "${BLUE}ℹ️  $message${NC}"
            ;;
    esac
}

# Function to check if GitHub CLI is installed
check_gh_cli() {
    if ! command -v gh &> /dev/null; then
        print_status "ERROR" "GitHub CLI (gh) is not installed"
        echo "Please install GitHub CLI: https://cli.github.com/"
        exit 1
    fi
    
    print_status "SUCCESS" "GitHub CLI is installed"
}

# Function to check if user is authenticated
check_gh_auth() {
    if ! gh auth status &> /dev/null; then
        print_status "ERROR" "Not authenticated with GitHub CLI"
        echo "Please run: gh auth login"
        exit 1
    fi
    
    print_status "SUCCESS" "Authenticated with GitHub CLI"
}

# Function to get repository info
get_repo_info() {
    if [ -z "$GITHUB_REPOSITORY" ]; then
        # Try to get from git remote
        REPO_URL=$(git remote get-url origin 2>/dev/null || echo "")
        if [[ $REPO_URL =~ github\.com[:/]([^/]+)/([^/.]+) ]]; then
            REPO_OWNER="${BASH_REMATCH[1]}"
            REPO_NAME="${BASH_REMATCH[2]}"
            GITHUB_REPOSITORY="$REPO_OWNER/$REPO_NAME"
        else
            print_status "ERROR" "Could not determine GitHub repository"
            echo "Please set GITHUB_REPOSITORY environment variable (e.g., owner/repo)"
            exit 1
        fi
    else
        IFS='/' read -r REPO_OWNER REPO_NAME <<< "$GITHUB_REPOSITORY"
    fi
    
    print_status "INFO" "Repository: $GITHUB_REPOSITORY"
}

# Function to check if environment exists
check_environment() {
    local env_name=$1
    local response
    
    response=$(gh api "repos/$GITHUB_REPOSITORY/environments/$env_name" 2>/dev/null || echo "NOT_FOUND")
    
    if [ "$response" = "NOT_FOUND" ]; then
        print_status "ERROR" "Environment '$env_name' does not exist"
        return 1
    else
        print_status "SUCCESS" "Environment '$env_name' exists"
        return 0
    fi
}

# Function to check environment protection rules
check_protection_rules() {
    local env_name=$1
    local response
    
    print_status "INFO" "Checking protection rules for '$env_name'..."
    
    response=$(gh api "repos/$GITHUB_REPOSITORY/environments/$env_name" 2>/dev/null || echo "NOT_FOUND")
    
    if [ "$response" = "NOT_FOUND" ]; then
        print_status "ERROR" "Cannot check protection rules - environment does not exist"
        return 1
    fi
    
    # Parse protection rules
    local has_reviewers=$(echo "$response" | jq -r '.protection_rules[]? | select(.type == "required_reviewers") | .type' 2>/dev/null || echo "")
    local reviewers=$(echo "$response" | jq -r '.protection_rules[]? | select(.type == "required_reviewers") | .reviewers[]?.reviewer.login' 2>/dev/null || echo "")
    local wait_timer=$(echo "$response" | jq -r '.protection_rules[]? | select(.type == "wait_timer") | .wait_timer' 2>/dev/null || echo "")
    
    if [ -n "$has_reviewers" ]; then
        print_status "SUCCESS" "Required reviewers protection rule is configured"
        if [ -n "$reviewers" ]; then
            print_status "INFO" "Reviewers: $reviewers"
            if echo "$reviewers" | grep -q "AshrafSyed25"; then
                print_status "SUCCESS" "@AshrafSyed25 is configured as a reviewer"
            else
                print_status "WARNING" "@AshrafSyed25 is NOT configured as a reviewer"
            fi
        else
            print_status "WARNING" "No specific reviewers configured"
        fi
    else
        print_status "ERROR" "No required reviewers protection rule configured"
    fi
    
    if [ -n "$wait_timer" ] && [ "$wait_timer" != "null" ]; then
        print_status "INFO" "Wait timer configured: $wait_timer minutes"
    else
        print_status "INFO" "No wait timer configured (immediate approval)"
    fi
}

# Function to provide setup instructions
provide_setup_instructions() {
    echo ""
    print_status "INFO" "Setup Instructions:"
    echo ""
    echo "1. Go to: https://github.com/$GITHUB_REPOSITORY/settings/environments"
    echo "2. Create environments if they don't exist:"
    echo "   - staging-approval"
    echo "   - production-approval"
    echo "3. Configure protection rules for each environment:"
    echo "   - Add 'AshrafSyed25' as required reviewer"
    echo "   - Set deployment branches to your main branch"
    echo "   - Enable 'Prevent administrators from bypassing'"
    echo ""
    echo "For detailed instructions, see: docs/github-environment-setup-guide.md"
}

# Main execution
main() {
    echo "🔍 GitHub Environment Verification for GitOps Approval Workflow"
    echo "=============================================================="
    echo ""
    
    # Check prerequisites
    check_gh_cli
    check_gh_auth
    get_repo_info
    
    echo ""
    print_status "INFO" "Checking GitHub environments..."
    echo ""
    
    # Check staging environment
    echo "📋 Staging Environment (staging-approval):"
    if check_environment "staging-approval"; then
        check_protection_rules "staging-approval"
    fi
    
    echo ""
    
    # Check production environment
    echo "📋 Production Environment (production-approval):"
    if check_environment "production-approval"; then
        check_protection_rules "production-approval"
    fi
    
    echo ""
    
    # Check workflow file
    if [ -f ".github/workflows/deploy-from-cicd.yaml" ]; then
        print_status "SUCCESS" "GitOps workflow file exists"
        
        # Check if environments are referenced in workflow
        if grep -q "environment: staging-approval" .github/workflows/deploy-from-cicd.yaml; then
            print_status "SUCCESS" "Staging approval environment is configured in workflow"
        else
            print_status "ERROR" "Staging approval environment is NOT configured in workflow"
        fi
        
        if grep -q "environment: production-approval" .github/workflows/deploy-from-cicd.yaml; then
            print_status "SUCCESS" "Production approval environment is configured in workflow"
        else
            print_status "ERROR" "Production approval environment is NOT configured in workflow"
        fi
    else
        print_status "ERROR" "GitOps workflow file not found: .github/workflows/deploy-from-cicd.yaml"
    fi
    
    echo ""
    provide_setup_instructions
}

# Run main function
main "$@"
