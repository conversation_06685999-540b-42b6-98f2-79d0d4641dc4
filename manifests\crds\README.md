# Custom Resource Definitions (CRDs)

This directory contains Custom Resource Definitions that need to be installed in the cluster before deploying applications.

## Included CRDs

### External Secrets Operator
- **Purpose**: Manage secrets from external secret management systems
- **Installation**: Apply CRDs before deploying applications that use ExternalSecret resources
- **Documentation**: https://external-secrets.io/

### ArgoCD CRDs
- **Purpose**: ArgoCD Application and AppProject resources
- **Installation**: Installed automatically with ArgoCD
- **Documentation**: https://argo-cd.readthedocs.io/

## Installation Order

1. Install CRDs first:
   ```bash
   kubectl apply -f manifests/crds/
   ```

2. Install ArgoCD (if not already installed):
   ```bash
   kubectl create namespace argocd
   kubectl apply -n argocd -f https://raw.githubusercontent.com/argoproj/argo-cd/stable/manifests/install.yaml
   ```

3. Apply ArgoCD Project:
   ```bash
   kubectl apply -f manifests/argocd/argocd-project.yaml
   ```

4. Deploy applications using Kustomize overlays:
   ```bash
   # Development
   kubectl apply -k manifests/overlays/dev/
   
   # Staging
   kubectl apply -k manifests/overlays/staging/
   
   # Production
   kubectl apply -k manifests/overlays/production/
   ```

## Adding New CRDs

When adding new CRDs:

1. Place the CRD YAML files in this directory
2. Update this README with installation instructions
3. Ensure the ArgoCD project has appropriate permissions for the new resources
4. Test the installation order in a development environment

## Platform-Specific CRDs

### DigitalOcean Kubernetes (DOKS)
- **CSI Driver**: Automatically installed
- **Load Balancer Controller**: Automatically installed
- **Cluster Autoscaler**: Available as add-on

### External Dependencies
- **Cert-Manager**: For TLS certificate management
- **Ingress Controllers**: NGINX, Traefik, etc.
- **Monitoring**: Prometheus Operator CRDs
