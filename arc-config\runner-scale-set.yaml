apiVersion: actions.summerwind.dev/v1alpha1
kind: RunnerDeployment
metadata:
  name: chidhagni-organisation-runner
  namespace: ch-arc-runner-system
  labels:
    app: chidhagni-organisation-runner
    component: actions-runner
    environment: doks
spec:
  # Number of runner replicas optimized for DOKS
  replicas: 1

  # Template for runner pods
  template:
    metadata:
      labels:
        app: chidhagni-organisation-runner
        component: actions-runner
    spec:
      organization: ChidhagniConsulting

      # Runner labels for workflow targeting - UPDATED FOR DOKS
      labels:
        - self-hosted
        - linux

      # Use the gitops service account with cluster permissions
      serviceAccountName: gitops-runner-sa

      # Add environment for kubectl config
      env:
        - name: KUBECONFIG
          value: "/var/run/secrets/kubernetes.io/serviceaccount"
        - name: KUBERNETES_SERVICE_HOST
          valueFrom:
            fieldRef:
              fieldPath: status.hostIP

      # Resource limits optimized for DOKS
      resources:
        requests:
          memory: "512Mi"
          cpu: "200m"
        limits:
          memory: "1Gi"
          cpu: "500m"

      # Environment variables
      env:
        - name: RUNNER_SCOPE
          value: "org"
        - name: DISABLE_RUNNER_UPDATE
          value: "true"
        - name: RUNNER_FEATURE_FLAG_ONCE
          value: "true"

      # Disable Docker for now (focus on kubectl/ArgoCD access)
      dockerEnabled: false

      # Volume mounts for kubectl cache
      volumeMounts:
        - name: kubectl-cache
          mountPath: /home/<USER>/.kube
        - name: kube-api-access
          mountPath: /var/run/secrets/kubernetes.io/serviceaccount
          readOnly: true

      # Volumes
      volumes:
        - name: kubectl-cache
          emptyDir: {}
        - name: kube-api-access
          projected:
            sources:
            - serviceAccountToken:
                path: token
            - configMap:
                name: kube-root-ca.crt
                items:
                - key: ca.crt
                  path: ca.crt
            - downwardAPI:
                items:
                - path: namespace
                  fieldRef:
                    fieldPath: metadata.namespace

      # Node selector for Linux nodes
      nodeSelector:
        kubernetes.io/os: linux

      # Security context
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000

      # Termination grace period
      terminationGracePeriodSeconds: 30

---
# HorizontalRunnerAutoscaler for scaling based on load
apiVersion: actions.summerwind.dev/v1alpha1
kind: HorizontalRunnerAutoscaler
metadata:
  name: chidhagni-organisation-runner-hra
  namespace: ch-arc-runner-system
  labels:
    app: chidhagni-organisation-runner
    component: autoscaler
spec:
  scaleTargetRef:
    kind: RunnerDeployment
    name: chidhagni-organisation-runner

  # Scale between 1-5 runners for DOKS
  minReplicas: 1
  maxReplicas: 5

  # Scaling metrics - scale up when there are pending workflow runs
  metrics:
  - type: PercentageRunnersBusy
    scaleUpThreshold: '0.75'
    scaleDownThreshold: '0.25'
    scaleUpFactor: '2'
    scaleDownFactor: '0.5'

  # Scale down delay to prevent rapid scaling
  scaleDownDelaySecondsAfterScaleOut: 300


