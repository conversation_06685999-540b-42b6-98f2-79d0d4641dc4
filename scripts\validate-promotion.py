#!/usr/bin/env python3
"""
GitOps Promotion Validation and Rollback Script
Provides validation checks and rollback capabilities for environment promotions
"""

import argparse
import json
import os
import sys
import yaml
import subprocess
import requests
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from datetime import datetime


def print_status(message: str, status_type: str = "INFO"):
    """Print colored status messages"""
    colors = {
        "SUCCESS": "\033[32m[SUCCESS]",
        "ERROR": "\033[31m[ERROR]",
        "WARNING": "\033[33m[WARNING]",
        "INFO": "\033[36m[INFO]"
    }
    reset = "\033[0m"
    color = colors.get(status_type, "")
    print(f"{color} {message}{reset}")


def check_kubectl_connectivity() -> bool:
    """Check if kubectl is available and can connect to cluster"""
    try:
        result = subprocess.run(['kubectl', 'cluster-info'], 
                              capture_output=True, text=True, timeout=10)
        return result.returncode == 0
    except (subprocess.TimeoutExpired, FileNotFoundError):
        return False


def get_argocd_application_status(project_id: str, environment: str) -> Optional[Dict]:
    """Get ArgoCD application status"""
    if not check_kubectl_connectivity():
        print_status("kubectl not available or cluster not accessible", "WARNING")
        return None
    
    try:
        cmd = ['kubectl', 'get', 'application', f'{project_id}-{environment}', 
               '-n', 'argocd', '-o', 'json']
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            return json.loads(result.stdout)
        else:
            print_status(f"Application {project_id}-{environment} not found in ArgoCD", "WARNING")
            return None
    except Exception as e:
        print_status(f"Error getting ArgoCD application status: {e}", "ERROR")
        return None


def validate_image_exists(image: str) -> bool:
    """Validate that Docker image exists and is accessible"""
    print_status(f"Validating image accessibility: {image}", "INFO")
    
    try:
        # Try to inspect the image (this will pull if not available locally)
        result = subprocess.run(['docker', 'image', 'inspect', image], 
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print_status(f"Image {image} is accessible", "SUCCESS")
            return True
        else:
            # Try to pull the image
            print_status(f"Attempting to pull image: {image}", "INFO")
            pull_result = subprocess.run(['docker', 'pull', image], 
                                       capture_output=True, text=True, timeout=300)
            
            if pull_result.returncode == 0:
                print_status(f"Successfully pulled image: {image}", "SUCCESS")
                return True
            else:
                print_status(f"Failed to pull image: {image}", "ERROR")
                print_status(f"Error: {pull_result.stderr}", "ERROR")
                return False
                
    except subprocess.TimeoutExpired:
        print_status(f"Timeout while validating image: {image}", "ERROR")
        return False
    except FileNotFoundError:
        print_status("Docker not available for image validation", "WARNING")
        return True  # Assume valid if we can't check


def validate_target_environment(project_id: str, target_env: str) -> bool:
    """Validate target environment configuration"""
    print_status(f"Validating target environment: {target_env}", "INFO")
    
    # Check if target environment files exist
    target_patch = Path(f"deployments/{project_id}/overlays/{target_env}/patch-image.yaml")
    target_app = Path(f"deployments/{project_id}/overlays/{target_env}/application.yaml")
    target_kustomization = Path(f"deployments/{project_id}/overlays/{target_env}/kustomization.yaml")
    
    missing_files = []
    if not target_patch.exists():
        missing_files.append(str(target_patch))
    if not target_app.exists():
        missing_files.append(str(target_app))
    if not target_kustomization.exists():
        missing_files.append(str(target_kustomization))
    
    if missing_files:
        print_status(f"Missing target environment files: {missing_files}", "ERROR")
        return False
    
    # Validate YAML syntax
    for file_path in [target_patch, target_app, target_kustomization]:
        try:
            with open(file_path, 'r') as f:
                yaml.safe_load(f)
        except yaml.YAMLError as e:
            print_status(f"Invalid YAML in {file_path}: {e}", "ERROR")
            return False
    
    print_status(f"Target environment {target_env} validation passed", "SUCCESS")
    return True


def check_deployment_health(project_id: str, environment: str) -> Dict[str, str]:
    """Check deployment health in target environment"""
    health_status = {
        "status": "unknown",
        "message": "Health check not performed",
        "pods_ready": "unknown",
        "last_sync": "unknown"
    }
    
    if not check_kubectl_connectivity():
        health_status["message"] = "kubectl not available"
        return health_status
    
    try:
        # Check ArgoCD application status
        app_status = get_argocd_application_status(project_id, environment)
        if app_status:
            status = app_status.get('status', {})
            health_status["status"] = status.get('health', {}).get('status', 'unknown')
            health_status["last_sync"] = status.get('operationState', {}).get('finishedAt', 'unknown')
            
            # Check sync status
            sync_status = status.get('sync', {}).get('status', 'unknown')
            health_status["sync_status"] = sync_status
        
        # Check pod status in namespace
        namespace = f"{project_id}-{environment}"
        cmd = ['kubectl', 'get', 'pods', '-n', namespace, '-o', 'json']
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            pods_data = json.loads(result.stdout)
            pods = pods_data.get('items', [])
            
            ready_pods = 0
            total_pods = len(pods)
            
            for pod in pods:
                conditions = pod.get('status', {}).get('conditions', [])
                for condition in conditions:
                    if condition.get('type') == 'Ready' and condition.get('status') == 'True':
                        ready_pods += 1
                        break
            
            health_status["pods_ready"] = f"{ready_pods}/{total_pods}"
            
            if total_pods > 0 and ready_pods == total_pods:
                health_status["message"] = "All pods are ready"
            elif ready_pods > 0:
                health_status["message"] = f"Partially ready: {ready_pods}/{total_pods} pods"
            else:
                health_status["message"] = "No pods are ready"
        
    except Exception as e:
        health_status["message"] = f"Error checking health: {e}"
    
    return health_status


def create_rollback_plan(project_id: str, environment: str, current_image: str) -> Dict:
    """Create a rollback plan for the promotion"""
    rollback_plan = {
        "timestamp": datetime.now().isoformat(),
        "project_id": project_id,
        "environment": environment,
        "current_image": current_image,
        "rollback_commands": []
    }
    
    # Add rollback commands
    rollback_plan["rollback_commands"] = [
        f"# Rollback {project_id} in {environment} to {current_image}",
        f"python3 scripts/promote-image.py --project-id {project_id} --source-env {environment} --target-env {environment} --manual",
        f"# Or use direct kubectl commands:",
        f"kubectl patch application {project_id}-{environment} -n argocd --type merge -p '{{\"spec\":{{\"source\":{{\"targetRevision\":\"main\"}}}}}}'",
        f"kubectl annotate application {project_id}-{environment} -n argocd argocd.argoproj.io/refresh=normal"
    ]
    
    return rollback_plan


def save_rollback_plan(rollback_plan: Dict, output_dir: str = "rollback-plans"):
    """Save rollback plan to file"""
    os.makedirs(output_dir, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
    filename = f"{rollback_plan['project_id']}-{rollback_plan['environment']}-{timestamp}.json"
    filepath = Path(output_dir) / filename
    
    with open(filepath, 'w') as f:
        json.dump(rollback_plan, f, indent=2)
    
    print_status(f"Rollback plan saved: {filepath}", "SUCCESS")
    return filepath


def run_pre_promotion_validation(project_id: str, source_env: str, target_env: str, 
                                source_image: str) -> bool:
    """Run comprehensive pre-promotion validation"""
    print_status("Running pre-promotion validation...", "INFO")
    
    validation_results = []
    
    # 1. Validate target environment configuration
    validation_results.append(validate_target_environment(project_id, target_env))
    
    # 2. Validate source image accessibility
    validation_results.append(validate_image_exists(source_image))
    
    # 3. Check source environment health
    source_health = check_deployment_health(project_id, source_env)
    if source_health["status"] in ["Healthy", "unknown"]:
        print_status(f"Source environment health: {source_health['status']}", "SUCCESS")
        validation_results.append(True)
    else:
        print_status(f"Source environment unhealthy: {source_health['status']}", "WARNING")
        validation_results.append(False)
    
    # 4. Check if target environment is already deployed
    target_health = check_deployment_health(project_id, target_env)
    if target_health["status"] != "unknown":
        print_status(f"Target environment current status: {target_health['status']}", "INFO")
    
    all_passed = all(validation_results)
    
    if all_passed:
        print_status("✅ All pre-promotion validations passed", "SUCCESS")
    else:
        print_status("❌ Some pre-promotion validations failed", "ERROR")
    
    return all_passed


def main():
    parser = argparse.ArgumentParser(description='Validate promotions and create rollback plans')
    parser.add_argument('--project-id', required=True, help='Project ID')
    parser.add_argument('--source-env', default='dev', help='Source environment')
    parser.add_argument('--target-env', required=True, help='Target environment')
    parser.add_argument('--source-image', help='Source image to validate')
    parser.add_argument('--create-rollback-plan', action='store_true', 
                       help='Create rollback plan for current deployment')
    parser.add_argument('--health-check', action='store_true', 
                       help='Check deployment health')
    parser.add_argument('--pre-promotion-check', action='store_true', 
                       help='Run comprehensive pre-promotion validation')
    
    args = parser.parse_args()
    
    if args.health_check:
        print_status(f"Checking health for {args.project_id} in {args.target_env}", "INFO")
        health = check_deployment_health(args.project_id, args.target_env)
        print(json.dumps(health, indent=2))
    
    if args.create_rollback_plan:
        if not args.source_image:
            print_status("Source image required for rollback plan creation", "ERROR")
            sys.exit(1)
        
        rollback_plan = create_rollback_plan(args.project_id, args.target_env, args.source_image)
        save_rollback_plan(rollback_plan)
    
    if args.pre_promotion_check:
        if not args.source_image:
            print_status("Source image required for pre-promotion validation", "ERROR")
            sys.exit(1)
        
        success = run_pre_promotion_validation(args.project_id, args.source_env, 
                                             args.target_env, args.source_image)
        sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
