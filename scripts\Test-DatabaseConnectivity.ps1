#!/usr/bin/env pwsh

# Database Connectivity Test Script
# This script tests PostgreSQL connectivity using the configuration from Kustomize manifests

param(
    [Parameter(Mandatory=$false)]
    [string]$Environment = "all",
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipKubernetesTest
)

# Function to print colored output
function Write-Status {
    param(
        [string]$Status,
        [string]$Message
    )
    
    switch ($Status) {
        "SUCCESS" { Write-Host "✅ $Message" -ForegroundColor Green }
        "ERROR" { Write-Host "❌ $Message" -ForegroundColor Red }
        "WARNING" { Write-Host "⚠️ $Message" -ForegroundColor Yellow }
        default { Write-Host "$Message" }
    }
}

# Function to validate database configuration
function Test-DatabaseConfiguration {
    Write-Host ""
    Write-Host "🔍 Validating database configuration in manifests..." -ForegroundColor Cyan
    
    # Check base ConfigMap
    $configMapPath = "manifests/base/configmap.yaml"
    if (Test-Path $configMapPath) {
        $configMapContent = Get-Content $configMapPath -Raw
        
        # Extract database configuration
        $dbHost = ($configMapContent | Select-String 'DB_HOST:\s*"([^"]*)"').Matches[0].Groups[1].Value
        $dbPort = ($configMapContent | Select-String 'DB_PORT:\s*"([^"]*)"').Matches[0].Groups[1].Value
        $dbSslMode = ($configMapContent | Select-String 'DB_SSL_MODE:\s*"([^"]*)"').Matches[0].Groups[1].Value
        
        Write-Host "  Base ConfigMap DB_HOST: $dbHost"
        Write-Host "  Base ConfigMap DB_PORT: $dbPort"
        Write-Host "  Base ConfigMap DB_SSL_MODE: $dbSslMode"
        
        if ($dbSslMode -eq "require") {
            Write-Status "SUCCESS" "SSL mode is properly configured as 'require'"
        } else {
            Write-Status "WARNING" "SSL mode is not set to 'require' (current: $dbSslMode)"
        }
        
        if ($dbHost -like "*PLACEHOLDER*") {
            Write-Status "SUCCESS" "Database host is using placeholder (will be replaced by workflow)"
        } else {
            Write-Status "WARNING" "Database host appears to be hardcoded: $dbHost"
        }
        
    } else {
        Write-Status "ERROR" "Base ConfigMap not found"
        return $false
    }
    
    # Check base Secret
    $secretPath = "manifests/base/secret.yaml"
    if (Test-Path $secretPath) {
        $secretContent = Get-Content $secretPath -Raw
        
        if ($secretContent -match "DB_PASSWORD:") {
            Write-Status "SUCCESS" "Database password secret is configured"
        } else {
            Write-Status "ERROR" "Database password secret not found"
        }
        
        if ($secretContent -match "DB_SSL_MODE:") {
            Write-Status "SUCCESS" "Database SSL mode secret is configured"
        } else {
            Write-Status "WARNING" "Database SSL mode secret not found (using ConfigMap value)"
        }
    } else {
        Write-Status "ERROR" "Base Secret not found"
        return $false
    }
    
    return $true
}

# Function to test Kustomize overlay builds
function Test-KustomizeOverlays {
    Write-Host ""
    Write-Host "🔍 Testing Kustomize overlay builds..." -ForegroundColor Cyan
    
    $environments = @("dev", "staging", "production")
    $buildErrors = 0
    
    foreach ($env in $environments) {
        Write-Host "  Testing $env overlay..."
        
        try {
            $result = kubectl kustomize "manifests/overlays/$env" 2>&1
            if ($LASTEXITCODE -eq 0) {
                Write-Status "SUCCESS" "$env overlay builds successfully"
            } else {
                Write-Status "ERROR" "$env overlay build failed: $result"
                $buildErrors++
            }
        } catch {
            Write-Status "ERROR" "$env overlay build failed: $($_.Exception.Message)"
            $buildErrors++
        }
    }
    
    if ($buildErrors -eq 0) {
        Write-Status "SUCCESS" "All overlay builds passed"
        return $true
    } else {
        Write-Status "ERROR" "$buildErrors overlay build(s) failed"
        return $false
    }
}

# Function to validate database init container configuration
function Test-DatabaseInitContainer {
    Write-Host ""
    Write-Host "🔍 Testing database init container configuration..." -ForegroundColor Cyan

    $environments = @("dev", "staging", "production")
    $allSuccess = $true

    foreach ($env in $environments) {
        $overlayPath = "manifests/overlays/$env"
        if (Test-Path "$overlayPath/init-container-patch.yaml") {
            Write-Host "  Testing $env environment init container patch..." -ForegroundColor Yellow

            # Check if the patch file contains proper PostgreSQL configuration
            $patchContent = Get-Content "$overlayPath/init-container-patch.yaml" -Raw
            if ($patchContent -match "pg_isready") {
                Write-Status "SUCCESS" "$env environment: Init container uses pg_isready for database readiness check"
            } else {
                Write-Status "WARNING" "$env environment: Init container may not have proper readiness check"
                $allSuccess = $false
            }

            # Check environment-specific configuration
            if ($patchContent -match "ENVIRONMENT.*$env") {
                Write-Status "SUCCESS" "$env environment: Environment variable correctly set"
            } else {
                Write-Status "WARNING" "$env environment: Environment variable may not be set correctly"
            }
        } else {
            Write-Status "INFO" "$env environment: No init container patch found (may not be needed for this environment)"
        }
    }

    # Test the database-init component (now only contains environment-specific patches)
    $componentPath = "manifests/components/database-init"
    if (Test-Path "$componentPath/kustomization.yaml") {
        try {
            $result = kubectl kustomize $componentPath 2>&1
            if ($LASTEXITCODE -eq 0) {
                Write-Status "SUCCESS" "Database init component builds successfully"
            } else {
                Write-Status "ERROR" "Database init component build failed: $result"
                $allSuccess = $false
            }
        } catch {
            Write-Status "ERROR" "Database init component build failed: $($_.Exception.Message)"
            $allSuccess = $false
        }
    } else {
        Write-Status "WARNING" "Database init component not found"
        $allSuccess = $false
    }

    return $allSuccess
}

# Function to test SSL configuration
function Test-SSLConfiguration {
    Write-Host ""
    Write-Host "🔍 Testing SSL configuration..." -ForegroundColor Cyan
    
    # Check if SSL mode is properly configured in all overlays
    $environments = @("dev", "staging", "production")
    $sslErrors = 0
    
    foreach ($env in $environments) {
        try {
            $overlayOutput = kubectl kustomize "manifests/overlays/$env" 2>&1
            if ($LASTEXITCODE -eq 0) {
                if ($overlayOutput -match 'DB_SSL_MODE.*require') {
                    Write-Status "SUCCESS" "$env environment has SSL mode set to 'require'"
                } else {
                    Write-Status "WARNING" "$env environment SSL mode configuration not found or incorrect"
                    $sslErrors++
                }
            } else {
                Write-Status "ERROR" "Failed to build $env overlay for SSL check"
                $sslErrors++
            }
        } catch {
            Write-Status "ERROR" "Failed to check SSL configuration for $env: $($_.Exception.Message)"
            $sslErrors++
        }
    }
    
    if ($sslErrors -eq 0) {
        Write-Status "SUCCESS" "SSL configuration validated for all environments"
        return $true
    } else {
        Write-Status "WARNING" "$sslErrors environment(s) have SSL configuration issues"
        return $false
    }
}

# Main function
function Main {
    Write-Host "🚀 DigitalOcean PostgreSQL Connectivity Test" -ForegroundColor Green
    Write-Host "============================================" -ForegroundColor Green
    
    # Check if we're in the right directory
    if (-not (Test-Path "manifests")) {
        Write-Status "ERROR" "manifests directory not found. Please run from repository root."
        exit 1
    }
    
    # Check if kubectl is available
    try {
        kubectl version --client --short | Out-Null
        Write-Status "SUCCESS" "kubectl is available"
    } catch {
        Write-Status "WARNING" "kubectl not available, skipping Kubernetes-specific tests"
    }
    
    $allTestsPassed = $true
    
    # Test database configuration
    if (-not (Test-DatabaseConfiguration)) {
        $allTestsPassed = $false
    }
    
    # Test Kustomize overlays
    if (-not (Test-KustomizeOverlays)) {
        $allTestsPassed = $false
    }
    
    # Test database init container
    if (-not (Test-DatabaseInitContainer)) {
        $allTestsPassed = $false
    }
    
    # Test SSL configuration
    if (-not (Test-SSLConfiguration)) {
        $allTestsPassed = $false
    }
    
    Write-Host ""
    Write-Host "============================================" -ForegroundColor Green
    
    if ($allTestsPassed) {
        Write-Status "SUCCESS" "Database configuration validation completed successfully!"
        
        Write-Host ""
        Write-Host "📋 Summary:" -ForegroundColor Cyan
        Write-Host "✅ Database configuration uses placeholders for dynamic injection"
        Write-Host "✅ SSL mode is properly configured as 'require'"
        Write-Host "✅ All Kustomize overlays build successfully"
        Write-Host "✅ Secret management is properly configured"
        Write-Host "✅ Database init container is properly configured"
        
        Write-Host ""
        Write-Host "🔧 To test with actual database credentials:" -ForegroundColor Yellow
        Write-Host "1. Update the GitHub Actions workflow to inject real DB credentials"
        Write-Host "2. Deploy to a test environment with: kubectl apply -k manifests/overlays/dev"
        Write-Host "3. Check pod logs for database connectivity"
        
        Write-Host ""
        Write-Host "💡 Example workflow_dispatch payload:" -ForegroundColor Cyan
        Write-Host @"
{
  "db_host": "your-db-host.digitalocean.com",
  "db_port": "25060",
  "db_name": "your_database",
  "db_user": "your_user",
  "db_password": "your_password"
}
"@
        
        exit 0
    } else {
        Write-Status "ERROR" "Some database configuration tests failed"
        Write-Host ""
        Write-Host "Please review the errors above and fix the configuration issues."
        exit 1
    }
}

# Run main function
Main
