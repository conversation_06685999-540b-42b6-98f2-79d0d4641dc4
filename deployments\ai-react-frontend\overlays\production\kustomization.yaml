apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: ai-react-frontend-production

resources:
- deployment.yaml
- service.yaml
- configmap.yaml
- secret.yaml

components:
- ../../components/common-labels

- ../../components/react-frontend


labels:
- pairs:
    app: ai-react-frontend
    app.kubernetes.io/name: ai-react-frontend
    app.kubernetes.io/part-of: AI React Frontend
    app.kubernetes.io/component: react-frontend
    app.kubernetes.io/version: "405e961c"
    app.kubernetes.io/managed-by: argocd
    environment: production
    source.repo: ChidhagniConsulting-ai-react-frontend
    source.branch: main

# Environment-specific configurations are now directly in the manifest files
# Database init container is now managed at the environment level via init-container-patch.yaml

patches:
- path: patch-image.yaml
  target:
    kind: Deployment
    name: ai-react-frontend




namePrefix: ""
nameSuffix: "-prod"
