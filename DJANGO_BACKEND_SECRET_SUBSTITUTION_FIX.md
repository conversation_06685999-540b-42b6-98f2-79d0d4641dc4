# Django Backend Secret Substitution Fix

## Overview

This document summarizes the fix implemented to resolve the issue where Django backend applications were not receiving dynamic secret substitution during manifest generation, while Spring Boot backend applications were working correctly.

## Problem Description

### Issue
Django backend applications in the GitOps ArgoCD setup were not receiving proper secret substitution during manifest generation. The secret templates contained `DYNAMIC_*_B64` placeholders that were not being replaced with actual base64-encoded values from the deployment payload, unlike Spring Boot applications which were working correctly.

### Root Cause
The base template files in `manifests/overlays/*/secret.yaml` were designed primarily for Spring Boot applications and included:
1. **Spring Boot specific secrets** like `SPRING_DATASOURCE_URL` that Django applications don't use
2. **Missing Django-specific secrets** like `SESSION_SECRET`, `RATE_LIMIT_WINDOW_MS`, etc.
3. **No conditional logic** to differentiate between application types

## Solution Implemented

### 1. Updated Base Secret Templates

Modified the following files to include conditional logic and Django-specific secrets:
- `manifests/overlays/dev/secret.yaml`
- `manifests/overlays/staging/secret.yaml`
- `manifests/overlays/production/secret.yaml`

#### Key Changes:

**A. Added Conditional Logic for Spring Boot Secrets:**
```yaml
{{#eq APPLICATION_TYPE "springboot-backend"}}
# Spring Boot specific secrets
SPRING_DATASOURCE_URL: DYNAMIC_SPRING_DATASOURCE_URL_B64
{{/eq}}
```

**B. Added Django-Specific Secrets:**
```yaml
{{#eq APPLICATION_TYPE "django-backend"}}
# Django-specific secrets
SESSION_SECRET: DYNAMIC_SESSION_SECRET_B64
RATE_LIMIT_WINDOW_MS: DYNAMIC_RATE_LIMIT_WINDOW_MS_B64
RATE_LIMIT_MAX_REQUESTS: DYNAMIC_RATE_LIMIT_MAX_REQUESTS_B64
PASSWORD_RESET_TOKEN_EXPIRY: DYNAMIC_PASSWORD_RESET_TOKEN_EXPIRY_B64
EMAIL_VERIFICATION_TOKEN_EXPIRY: DYNAMIC_EMAIL_VERIFICATION_TOKEN_EXPIRY_B64
JWT_EXPIRES_IN: DYNAMIC_JWT_EXPIRES_IN_B64
JWT_REFRESH_EXPIRES_IN: DYNAMIC_JWT_REFRESH_EXPIRES_IN_B64
{{/eq}}
```

### 2. Verified Script Compatibility

Confirmed that the existing `scripts/process_payload.py` and `scripts/deploy.py` already support Django backend applications correctly:
- ✅ Django backend is included in supported application types
- ✅ Secret encoding logic automatically creates `DYNAMIC_*_B64` placeholders for all secrets
- ✅ Handlebars conditional processing works correctly
- ✅ Container port defaults to 8000 for Django applications

## Django vs Spring Boot Secret Handling

### Django Backend Secrets
```json
{
  "SESSION_SECRET": "...",
  "RATE_LIMIT_WINDOW_MS": "900000",
  "RATE_LIMIT_MAX_REQUESTS": "100",
  "PASSWORD_RESET_TOKEN_EXPIRY": "3600",
  "EMAIL_VERIFICATION_TOKEN_EXPIRY": "86400",
  "JWT_SECRET": "...",
  "JWT_EXPIRES_IN": "3600",
  "JWT_REFRESH_EXPIRES_IN": "86400",
  "DB_NAME": "...",
  "DB_USER": "...",
  "DB_PASSWORD": "...",
  "DB_HOST": "...",
  "DB_PORT": "5432",
  "DB_SSL_MODE": "require",
  "SMTP_USER": "...",
  "SMTP_PASS": "...",
  "GOOGLE_CLIENT_ID": "...",
  "GOOGLE_CLIENT_SECRET": "..."
}
```

### Spring Boot Backend Secrets
```json
{
  "JWT_SECRET": "...",
  "DB_NAME": "...",
  "DB_USER": "...",
  "DB_PASSWORD": "...",
  "DB_HOST": "...",
  "DB_PORT": "5432",
  "DB_SSL_MODE": "require",
  "SMTP_USER": "...",
  "SMTP_PASS": "...",
  "GOOGLE_CLIENT_ID": "...",
  "GOOGLE_CLIENT_SECRET": "..."
}
```

### Key Differences

| Aspect | Django Backend | Spring Boot Backend |
|--------|----------------|-------------------|
| **Database URL** | Uses `DATABASE_URL` env var | Uses `SPRING_DATASOURCE_URL` secret |
| **JWT Configuration** | Includes `JWT_EXPIRES_IN`, `JWT_REFRESH_EXPIRES_IN` | Only `JWT_SECRET` |
| **Session Management** | Includes `SESSION_SECRET` | Not applicable |
| **Rate Limiting** | Includes `RATE_LIMIT_*` secrets | Not applicable |
| **Token Expiry** | Includes `*_TOKEN_EXPIRY` secrets | Not applicable |
| **Container Port** | Default: 8000 | Default: 8080 |

## Testing and Validation

### Test Results

**✅ Django Backend Secret Substitution Test:**
- All 18 secrets properly encoded and substituted
- No `DYNAMIC_*_B64` placeholders remain
- Django-specific secrets present in final manifests
- `SPRING_DATASOURCE_URL` correctly excluded

**✅ Spring Boot Backend Compatibility Test:**
- All Spring Boot secrets properly substituted
- `SPRING_DATASOURCE_URL` correctly included
- Django-specific secrets correctly excluded
- No regression in existing functionality

**✅ End-to-End Deployment Workflow Test:**
- Complete manifest generation pipeline works correctly
- Project directories created with proper structure
- Environment-specific overlays generated correctly
- Deployment configurations include application-specific settings

## Files Modified

### Base Templates
- `manifests/overlays/dev/secret.yaml`
- `manifests/overlays/staging/secret.yaml`
- `manifests/overlays/production/secret.yaml`

### Test Files Created
- `test_django_secret_substitution.py` - Basic secret substitution test
- `test_django_end_to_end.py` - Complete deployment workflow test
- `test_springboot_compatibility.py` - Spring Boot compatibility verification

## Usage

### Django Backend Payload Example
```json
{
  "project_id": "ai-django-backend-test",
  "container_port": 8000,
  "application_type": "django-backend",
  "environment": "dev",
  "docker_image": "registry.digitalocean.com/doks-registry/ai-django-backend",
  "docker_tag": "v1.0.0",
  "source_repo": "myorg/ai-django-backend",
  "source_branch": "main",
  "commit_sha": "abc123def456",
  "secrets_encoded": "<base64-encoded-secrets-json>"
}
```

### Deployment Command
```bash
python scripts/deploy.py \
  --payload '{"project_id": "ai-django-backend-test", ...}' \
  --manifest-dir "manifests" \
  --output-dir "generated-manifests" \
  --dry-run
```

## Impact

### ✅ Benefits
- Django backend applications now receive proper secret substitution
- Maintains full compatibility with Spring Boot applications
- No changes required to existing deployment scripts
- Application-type specific secret handling

### ⚠️ Considerations
- Existing Django backend deployments may need regeneration to use new templates
- Ensure all required Django secrets are included in CI/CD payloads
- Test thoroughly when updating existing Django applications

## Conclusion

The Django backend secret substitution issue has been successfully resolved through conditional templating in the base secret files. The solution maintains backward compatibility with Spring Boot applications while providing Django applications with their specific secret requirements. All tests pass, confirming that the fix works correctly across the entire GitOps deployment pipeline.
