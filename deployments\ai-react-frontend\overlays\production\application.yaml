apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: ai-react-frontend-production
  namespace: argocd
  labels:
    app.kubernetes.io/name: ai-react-frontend-production
    app.kubernetes.io/part-of: AI React Frontend
    app.kubernetes.io/component: react-frontend
    app.kubernetes.io/version: "405e961c"
    app.kubernetes.io/managed-by: argocd
    environment: production
    app-type: react-frontend
    source.repo: ChidhagniConsulting-ai-react-frontend
    source.branch: main
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: ai-react-frontend-project
  source:
    repoURL: https://github.com/ChidhagniConsulting/gitops-argocd-apps
    targetRevision: main
    path: deployments/ai-react-frontend/overlays/production
  destination:
    server: https://6be4e15d-52f9-431d-84ec-ec8cad0dff2d.k8s.ondigitalocean.com
    namespace: ai-react-frontend-production
  syncPolicy:
    automated:
      prune: false
      selfHeal: false
      allowEmpty: false
    syncOptions:
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true
    retry:
      limit: 3
      backoff:
        duration: 10s
        factor: 2
        maxDuration: 5m
  revisionHistoryLimit: 2
  ignoreDifferences:
  - group: apps
    kind: Deployment
    jsonPointers:
    - /spec/replicas
  info:
  - name: Description
    value: "Production environment for AI React Frontend"
  - name: Repository
    value: "https://github.com/ChidhagniConsulting/ai-react-frontend"
  - name: Environment
    value: "production"
  - name: Application Type
    value: "react-frontend"
  - name: Source Branch
    value: "main"
  - name: Commit SHA
    value: "405e961c"
  - name: Configuration
    value: "Production configuration with high availability and security"
