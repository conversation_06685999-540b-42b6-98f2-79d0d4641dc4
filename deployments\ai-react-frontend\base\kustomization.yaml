apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: ai-react-frontend

resources:
- deployment.yaml
- service.yaml
- configmap.yaml

labels:
- pairs:
    app: ai-react-frontend
    app.kubernetes.io/name: ai-react-frontend
    app.kubernetes.io/part-of: AI React Frontend
    app.kubernetes.io/managed-by: argocd
    source.repo: ChidhagniConsulting-ai-react-frontend
    source.branch: main

commonAnnotations:
  app.kubernetes.io/managed-by: kustomize
  source.commit: 53a0a299

namePrefix: ""
nameSuffix: ""

images:
- name: registry.digitalocean.com/doks-registry/ai-react-frontend:latest
  newName: registry.digitalocean.com/doks-registry/ai-react-frontend:latest

replicas:
- name: ai-react-frontend
  count: 1
