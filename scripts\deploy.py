#!/usr/bin/env python3
"""
Unified Cross-Platform Deployment Entry Point
Automatically detects OS and deploys applications using Kustomize with dynamic secrets
"""

import argparse
import os
import sys
import platform
import subprocess
import shutil
import tempfile
import json
from pathlib import Path


def print_status(message, status_type="INFO"):
    """Print colored status messages"""
    colors = {
        "SUCCESS": "\033[32m[SUCCESS]",
        "ERROR": "\033[31m[ERROR]",
        "WARNING": "\033[33m[WARNING]",
        "INFO": "\033[36m[INFO]"
    }
    reset = "\033[0m"
    color = colors.get(status_type, "")
    print(f"{color} {message}{reset}")


def detect_platform():
    """Detect the current platform and return normalized info"""
    system = platform.system().lower()
    machine = platform.machine().lower()
    
    platform_info = {
        "system": system,
        "machine": machine,
        "is_windows": system == "windows",
        "is_linux": system == "linux", 
        "is_macos": system == "darwin",
        "path_separator": "\\" if system == "windows" else "/",
        "executable_extension": ".exe" if system == "windows" else ""
    }
    
    print_status(f"Detected platform: {system} ({machine})", "INFO")
    return platform_info


def check_prerequisites(platform_info):
    """Check if required tools are available"""
    print_status("Checking prerequisites...", "INFO")
    
    required_tools = ["kubectl", "python3" if not platform_info["is_windows"] else "python"]
    missing_tools = []
    
    for tool in required_tools:
        tool_name = tool + platform_info["executable_extension"]
        if not shutil.which(tool_name):
            missing_tools.append(tool)
    
    if missing_tools:
        print_status(f"Missing required tools: {', '.join(missing_tools)}", "ERROR")
        print("\nInstallation instructions:")
        for tool in missing_tools:
            if tool == "kubectl":
                print("  kubectl: https://kubernetes.io/docs/tasks/tools/")
            elif tool in ["python3", "python"]:
                print("  Python: https://www.python.org/downloads/")
        return False
    
    print_status("All prerequisites available", "SUCCESS")
    return True


def parse_github_payload(args):
    """Parse GitHub Actions payload and convert to deployment arguments"""
    try:
        import json

        # Parse payload JSON
        payload = json.loads(args.payload)

        # Extract container image and tag
        docker_image = payload.get('docker_image', 'nginx')
        docker_tag = payload.get('docker_tag', 'latest')
        container_image = f"{docker_image}:{docker_tag}"

        # Create deployment arguments object
        class PayloadArgs:
            def __init__(self):
                self.project_id = payload.get('project_id', 'unknown-project')
                # Use project_id for app_name since app_name field is removed from payload
                self.app_name = payload.get('project_id', 'unknown-project')
                self.environment = payload.get('environment', 'dev')
                self.container_image = container_image
                self.app_type = payload.get('application_type', 'web-app')
                self.source_repo = payload.get('source_repo', 'unknown/repo')
                self.source_branch = payload.get('source_branch', 'main')
                self.container_port = payload.get('container_port', 8080)
                self.commit_sha = payload.get('commit_sha', 'unknown')
                # Use secrets from payload if available, otherwise use command line argument
                self.secrets_encoded = payload.get('secrets_encoded') or args.secrets_encoded
                self.manifest_dir = args.manifest_dir
                self.output_dir = args.output_dir
                self.dry_run = args.dry_run

        deployment_args = PayloadArgs()

        print_status(f"Parsed payload for {deployment_args.app_name} ({deployment_args.project_id})", "SUCCESS")
        return deployment_args

    except json.JSONDecodeError as e:
        print_status(f"Failed to parse payload JSON: {e}", "ERROR")
        return None
    except Exception as e:
        print_status(f"Error processing payload: {e}", "ERROR")
        return None


def validate_inputs(args):
    """Validate input parameters"""
    print_status("Validating input parameters...", "INFO")

    # Validate project ID format
    import re
    if not re.match(r'^[a-z0-9-]+$', args.project_id):
        print_status("Project ID must be lowercase alphanumeric with hyphens only", "ERROR")
        return False

    # Check if manifests directory exists
    manifest_dir = getattr(args, 'manifest_dir', 'manifests')
    if not Path(manifest_dir).exists():
        print_status(f"Manifest directory not found: {manifest_dir}. Please check the path.", "ERROR")
        return False

    # Check if environment overlay exists
    overlay_path = Path(f"{manifest_dir}/overlays/{args.environment}")
    if not overlay_path.exists():
        print_status(f"Environment overlay not found: {overlay_path}", "ERROR")
        return False

    print_status("Input validation passed", "SUCCESS")
    return True


def generate_dynamic_secrets(args, temp_dir):
    """Generate dynamic secrets for the application"""
    print_status("Generating dynamic secrets...", "INFO")
    
    # Prepare secret generation command
    secret_script = Path("scripts/generate_secrets.py")
    if not secret_script.exists():
        print_status("Secret generation script not found", "ERROR")
        return None
    
    cmd = [
        sys.executable, str(secret_script),
        "--project-id", args.project_id,
        "--app-type", args.app_type,
        "--environment", args.environment,
        "--output-dir", temp_dir
    ]
    
    # Add optional parameters
    if args.smtp_user:
        cmd.extend(["--smtp-user", args.smtp_user])
    if args.smtp_pass:
        cmd.extend(["--smtp-pass", args.smtp_pass])
    if args.google_client_id:
        cmd.extend(["--google-client-id", args.google_client_id])
    if args.google_client_secret:
        cmd.extend(["--google-client-secret", args.google_client_secret])
    
    # Add custom secrets
    if hasattr(args, 'custom_secrets') and args.custom_secrets:
        for key, value in args.custom_secrets.items():
            cmd.extend(["--custom-secret", key, value])
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print_status("Dynamic secrets generated successfully", "SUCCESS")
        
        # Find the generated patch file
        patch_files = list(Path(temp_dir).glob(f"secret-patch-{args.project_id}-{args.environment}.yaml"))
        if patch_files:
            return patch_files[0]
        else:
            print_status("Secret patch file not found", "ERROR")
            return None
            
    except subprocess.CalledProcessError as e:
        print_status(f"Failed to generate secrets: {e.stderr}", "ERROR")
        return None


def create_project_overlay(args, platform_info, secret_patch_file):
    """Create project-specific Kustomize overlay"""
    print_status("Creating project-specific overlay...", "INFO")

    manifest_dir = getattr(args, 'manifest_dir', 'manifests')
    source_overlay = Path(f"{manifest_dir}/overlays/{args.environment}")
    project_overlay = Path(f"{args.project_id}-{args.environment}")
    
    # Remove existing project overlay if it exists
    if project_overlay.exists():
        shutil.rmtree(project_overlay)
    
    # Copy environment overlay to project-specific directory
    shutil.copytree(source_overlay, project_overlay)
    
    # Copy secret patch to project overlay
    if secret_patch_file:
        shutil.copy2(secret_patch_file, project_overlay / "secret-patch.yaml")
    
    print_status(f"Created project overlay: {project_overlay}", "SUCCESS")
    return project_overlay


def customize_kustomize_config(project_overlay, args, platform_info):
    """Customize Kustomize configuration"""
    print_status("Customizing Kustomize configuration...", "INFO")
    
    os.chdir(project_overlay)
    
    try:
        # Set namespace
        subprocess.run(["kubectl", "kustomize", "edit", "set", "namespace", 
                       f"{args.project_id}-{args.environment}"], check=True)
        
        # Set name prefix
        subprocess.run(["kubectl", "kustomize", "edit", "set", "nameprefix", 
                       f"{args.project_id}-"], check=True)
        
        # Update image
        subprocess.run(["kubectl", "kustomize", "edit", "set", "image", 
                       f"app={args.container_image}"], check=True)
        
        # Add labels
        subprocess.run(["kubectl", "kustomize", "edit", "add", "label", 
                       f"app:{args.project_id}"], check=True)
        subprocess.run(["kubectl", "kustomize", "edit", "add", "label", 
                       f"environment:{args.environment}"], check=True)
        subprocess.run(["kubectl", "kustomize", "edit", "add", "label", 
                       f"app-type:{args.app_type}"], check=True)
        
        # Add secret patch to kustomization
        kustomization_file = Path("kustomization.yaml")
        with open(kustomization_file, "a") as f:
            f.write("\npatchesStrategicMerge:\n")
            f.write("- secret-patch.yaml\n")
        
        print_status("Kustomize configuration updated", "SUCCESS")
        
    except subprocess.CalledProcessError as e:
        print_status(f"Failed to customize Kustomize config: {e}", "ERROR")
        return False
    finally:
        os.chdir("..")
    
    return True


def build_and_validate_manifests(project_overlay, args, output_dir):
    """Build and validate Kubernetes manifests"""
    print_status("Building and validating manifests...", "INFO")

    # Check prerequisites first
    platform_info = detect_platform()
    if not check_prerequisites(platform_info):
        return None

    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)

    output_file = output_path / f"{args.project_id}-{args.environment}.yaml"

    # Verify the overlay directory exists and has required files
    overlay_path = Path(project_overlay)
    if not overlay_path.exists():
        print_status(f"Overlay directory does not exist: {project_overlay}", "ERROR")
        return None

    kustomization_file = overlay_path / "kustomization.yaml"
    if not kustomization_file.exists():
        print_status(f"kustomization.yaml not found in: {project_overlay}", "ERROR")
        return None

    print_status(f"Using overlay directory: {project_overlay}", "INFO")

    # Debug: Show directory contents
    try:
        import os
        print_status(f"Contents of {project_overlay}:", "INFO")
        for item in os.listdir(project_overlay):
            item_path = Path(project_overlay) / item
            if item_path.is_file():
                print_status(f"  📄 {item}", "INFO")
            else:
                print_status(f"  📁 {item}/", "INFO")
    except Exception as e:
        print_status(f"Could not list directory contents: {e}", "WARNING")

    try:
        # Debug: Show what we're trying to kustomize
        print_status(f"Running: kubectl kustomize {project_overlay}", "INFO")

        # Build manifests
        result = subprocess.run(["kubectl", "kustomize", str(overlay_path)],
                              capture_output=True, text=True, check=True, encoding='utf-8')
        
        # Ensure output is a string and write to file
        if not isinstance(result.stdout, str):
            print_status(f"Unexpected output type: {type(result.stdout)}. Converting to string.", "WARNING")
            stdout_str = str(result.stdout, 'utf-8', errors='replace')
        else:
            stdout_str = result.stdout

        with open(output_file, "w", encoding='utf-8') as f:
            f.write(stdout_str)
        
        # Validate YAML syntax (non-blocking)
        if not getattr(args, 'skip_validation', False):
            try:
                subprocess.run(["kubectl", "apply", "--dry-run=client", "-f", str(output_file)],
                              capture_output=True, text=True, check=True, encoding='utf-8', timeout=30)
                print_status("Manifest validation passed", "SUCCESS")
            except subprocess.CalledProcessError as e:
                print_status("Manifest validation had issues (continuing anyway):", "WARNING")
                if e.stderr:
                    error_lines = e.stderr.strip().split('\n')[:3]
                    for line in error_lines:
                        if line.strip():
                            print_status(f"  {line}", "WARNING")
            except subprocess.TimeoutExpired:
                print_status("Validation timed out (continuing anyway)", "WARNING")
        else:
            print_status("Validation skipped", "INFO")
        
        print_status(f"Manifests built and validated: {output_file}", "SUCCESS")
        return output_file
        
    except subprocess.CalledProcessError as e:
        print_status(f"Failed to build manifests: {e.stderr}", "WARNING")
        print_status(f"Command that failed: {e.cmd}", "WARNING")
        print_status(f"Return code: {e.returncode}", "WARNING")
        if e.stdout:
            print_status(f"Stdout: {e.stdout}", "WARNING")
        print_status("Continuing with deployment despite build issues", "WARNING")

        # Try to create a minimal output file for debugging
        try:
            with open(output_file, "w", encoding='utf-8') as f:
                f.write("# Manifest generation encountered issues\n")
                f.write("# Manual review may be required\n")
                f.write(f"# Error: {e.stderr}\n")
            return output_file
        except:
            return None
    except Exception as e:
        print_status(f"Unexpected error building manifests: {e}", "WARNING")
        return None

def process_payload_and_manifests(args, temp_dir):
    """Process GitHub Actions payload and replace placeholders in manifests"""
    print_status("Processing GitHub Actions payload...", "INFO")

    try:
        # Parse container image and tag safely
        if ':' in args.container_image:
            docker_image, docker_tag = args.container_image.rsplit(':', 1)
        else:
            docker_image = args.container_image
            docker_tag = 'latest'

        payload_data = {
            'project_id': args.project_id,
            'application_type': args.app_type,
            'environment': args.environment,
            'docker_image': docker_image,
            'docker_tag': docker_tag,
            'container_port': args.container_port,
            'source_repo': args.source_repo,
            'source_branch': args.source_branch,
            'commit_sha': args.commit_sha
        }
        payload_json = json.dumps(payload_data)

        process_script = Path("scripts/process_payload.py")
        if not process_script.exists():
            print_status("Payload processing script not found", "ERROR")
            return None

        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as payload_file:
            payload_file.write(payload_json)
            payload_file_path = payload_file.name

        cmd = [
            sys.executable, str(process_script),
            "--payload-file", payload_file_path,
            "--environment", args.environment,
            "--manifest-dir", args.manifest_dir,
            "--output-dir", temp_dir,
            "--validate"
        ]
        if args.secrets_encoded:
            cmd.extend(["--secrets", args.secrets_encoded])

        print_status(f"Running command: {' '.join(cmd)}", "INFO")
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print_status("Payload processing completed", "SUCCESS")

        if result.stdout:
            print_status("Process output:", "INFO")
            for line in result.stdout.split('\n'):
                if line.strip():
                    print_status(f"  {line}", "INFO")

        return Path(temp_dir) / args.project_id

    except subprocess.CalledProcessError as e:
        print_status(f"Payload processing failed with exit code {e.returncode}", "ERROR")
        print_status(f"Command that failed: {' '.join(e.cmd)}", "ERROR")
        if e.stdout:
            print_status("Process stdout:", "ERROR")
            for line in e.stdout.split('\n'):
                if line.strip():
                    print_status(f"  {line}", "ERROR")
        if e.stderr:
            print_status("Process stderr:", "ERROR")
            for line in e.stderr.split('\n'):
                if line.strip():
                    print_status(f"  {line}", "ERROR")
        return None
    except Exception as e:
        print_status(f"Unexpected error during payload processing: {e}", "ERROR")
        return None
    finally:
        Path(payload_file_path).unlink(missing_ok=True)


def build_and_validate_manifests_from_processed(processed_dir, args, output_dir):
    """Build and validate manifests from processed directory"""
    print_status("Building manifests from processed directory...", "INFO")

    platform_info = detect_platform()
    if not check_prerequisites(platform_info):
        return None

    overlay_path = processed_dir / "overlays" / args.environment
    if not overlay_path.exists():
        print_status(f"Processed overlay not found: {overlay_path}", "ERROR")
        return None

    kustomization_file = overlay_path / "kustomization.yaml"
    if not kustomization_file.exists():
        print_status(f"kustomization.yaml not found in: {overlay_path}", "ERROR")
        return None

    print_status(f"Using processed overlay directory: {overlay_path}", "INFO")

    try:
        import os
        print_status(f"Contents of {overlay_path}:", "INFO")
        for item in os.listdir(overlay_path):
            item_path = overlay_path / item
            if item_path.is_file():
                print_status(f"  📄 {item}", "INFO")
            else:
                print_status(f"  📁 {item}/", "INFO")
    except Exception as e:
        print_status(f"Could not list directory contents: {e}", "WARNING")

    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)

    output_file = output_path / f"{args.project_id}-{args.environment}.yaml"

    try:
        print_status(f"Running: kubectl kustomize {overlay_path}", "INFO")
        result = subprocess.run(["kubectl", "kustomize", str(overlay_path)],
                              capture_output=True, text=True, check=True, encoding='utf-8')
        
        if not isinstance(result.stdout, str):
            print_status(f"Unexpected output type: {type(result.stdout)}. Converting to string.", "WARNING")
            stdout_str = str(result.stdout, 'utf-8', errors='replace')
        else:
            stdout_str = result.stdout

        with open(output_file, "w", encoding='utf-8') as f:
            f.write(stdout_str)
        
        if not getattr(args, 'skip_validation', False):
            try:
                subprocess.run(["kubectl", "apply", "--dry-run=client", "-f", str(output_file)],
                              capture_output=True, text=True, check=True, encoding='utf-8', timeout=30)
                print_status("Manifest validation passed", "SUCCESS")
            except subprocess.CalledProcessError as e:
                print_status("Manifest validation had issues (continuing anyway):", "WARNING")
                if e.stderr:
                    error_lines = e.stderr.strip().split('\n')[:3]
                    for line in error_lines:
                        if line.strip():
                            print_status(f"  {line}", "WARNING")
            except subprocess.TimeoutExpired:
                print_status("Validation timed out (continuing anyway)", "WARNING")
        else:
            print_status("Validation skipped", "INFO")

        print_status(f"Manifests built and validated: {output_file}", "SUCCESS")
        return output_file

    except subprocess.CalledProcessError as e:
        print_status(f"Failed to build manifests: {e.stderr}", "WARNING")
        print_status(f"Command that failed: {e.cmd}", "WARNING")
        print_status(f"Return code: {e.returncode}", "WARNING")
        if e.stdout:
            print_status(f"Stdout: {e.stdout}", "WARNING")
        print_status("Continuing with deployment despite build issues", "WARNING")

        try:
            with open(output_file, "w", encoding='utf-8') as f:
                f.write("# Manifest generation encountered issues\n")
                f.write("# Manual review may be required\n")
                f.write(f"# Error: {e.stderr}\n")
            return output_file
        except:
            return None
    except Exception as e:
        print_status(f"Unexpected error building manifests: {e}", "WARNING")
        return None


def copy_processed_manifests_to_project_dir(processed_dir, args):
    """
    Copy processed manifests to final project directory structure under deployments/

    IMPORTANT: This function now preserves existing environment configurations
    and only updates the target environment to prevent cross-environment contamination.
    """
    print_status("Creating/updating project directory structure...", "INFO")

    # Create deployments directory structure
    deployments_dir = Path("deployments")
    deployments_dir.mkdir(exist_ok=True)

    project_dir = deployments_dir / args.project_id
    is_new_project = not project_dir.exists()

    try:
        if is_new_project:
            print_status(f"Creating new project directory: {project_dir}", "INFO")
            # Create project directory structure under deployments/
            project_dir.mkdir(exist_ok=True)
            argocd_dir = project_dir / "argocd"
            argocd_dir.mkdir(exist_ok=True)

            # Copy the complete Kustomize structure for new projects
            # Note: Base manifests are now integrated into environment-specific overlays

            overlays_source = processed_dir / "overlays"
            if overlays_source.exists():
                overlays_dest = project_dir / "overlays"
                shutil.copytree(overlays_source, overlays_dest, dirs_exist_ok=True)
                print_status(f"Copied overlays to {overlays_dest}", "SUCCESS")

            components_source = processed_dir / "components"
            if components_source.exists():
                components_dest = project_dir / "components"
                shutil.copytree(components_source, components_dest, dirs_exist_ok=True)
                print_status(f"Copied components to {components_dest}", "SUCCESS")

            # Copy ArgoCD manifests
            argocd_source = processed_dir / "argocd"
            if argocd_source.exists():
                for file in argocd_source.glob("*.yaml"):
                    if file.name == "argocd-project.yaml":
                        shutil.copy2(file, argocd_dir / "project.yaml")
                    else:
                        shutil.copy2(file, argocd_dir / file.name)
                print_status(f"Copied ArgoCD manifests to {argocd_dir}", "SUCCESS")

        else:
            print_status(f"Updating existing project directory: {project_dir}", "INFO")
            print_status(f"Target environment: {args.environment} (preserving other environments)", "INFO")

            # For existing projects, only update specific components to preserve other environments
            argocd_dir = project_dir / "argocd"
            argocd_dir.mkdir(exist_ok=True)

            # Note: Base manifests are now integrated into environment-specific overlays
            # No separate base directory update needed

            # Update components (these are shared across environments)
            components_source = processed_dir / "components"
            if components_source.exists():
                components_dest = project_dir / "components"
                if components_dest.exists():
                    shutil.rmtree(components_dest)
                shutil.copytree(components_source, components_dest, dirs_exist_ok=True)
                print_status(f"Updated components in {components_dest}", "SUCCESS")

            # CRITICAL: Only update the target environment's overlay directory
            target_overlay_source = processed_dir / "overlays" / args.environment
            if target_overlay_source.exists():
                overlays_dest = project_dir / "overlays"
                overlays_dest.mkdir(exist_ok=True)

                target_overlay_dest = overlays_dest / args.environment
                if target_overlay_dest.exists():
                    shutil.rmtree(target_overlay_dest)

                shutil.copytree(target_overlay_source, target_overlay_dest, dirs_exist_ok=True)
                print_status(f"Updated overlay for environment '{args.environment}' in {target_overlay_dest}", "SUCCESS")

                # List other environments that were preserved
                other_envs = [d.name for d in overlays_dest.iterdir() if d.is_dir() and d.name != args.environment]
                if other_envs:
                    print_status(f"Preserved existing environments: {', '.join(other_envs)}", "INFO")
            else:
                print_status(f"Warning: No overlay found for environment {args.environment}", "WARNING")

            # Update ArgoCD project manifest (shared across environments)
            argocd_source = processed_dir / "argocd"
            if argocd_source.exists():
                for file in argocd_source.glob("*.yaml"):
                    if file.name == "argocd-project.yaml":
                        shutil.copy2(file, argocd_dir / "project.yaml")
                    elif file.name != "application.yaml":  # Don't copy application.yaml from argocd dir
                        shutil.copy2(file, argocd_dir / file.name)
                print_status(f"Updated ArgoCD project manifest in {argocd_dir}", "SUCCESS")

        # Copy ArgoCD application from overlay (environment-specific)
        app_source = processed_dir / "overlays" / args.environment / "application.yaml"
        if app_source.exists():
            argocd_dir = project_dir / "argocd"
            shutil.copy2(app_source, argocd_dir / "application.yaml")
            print_status(f"Updated ArgoCD application for {args.environment} in {argocd_dir}", "SUCCESS")

        action = "created" if is_new_project else "updated"
        print_status(f"Project directory {action}: {project_dir}", "SUCCESS")
        return True

    except Exception as e:
        print_status(f"Failed to create/update project directory: {e}", "ERROR")
        return False


def deploy_processed_manifests(processed_dir, args):
    """Deploy processed manifests to Kubernetes cluster"""
    if args.dry_run:
        print_status("Dry run mode - skipping deployment", "INFO")
        return True

    print_status(f"Deploying processed manifests to {args.environment} cluster...", "INFO")

    overlay_path = processed_dir / "overlays" / args.environment

    try:
        # Apply manifests
        subprocess.run(["kubectl", "apply", "-k", str(overlay_path)], check=True)

        # Wait for deployment
        deployment_name = f"{args.project_id}"
        namespace = f"{args.project_id}-{args.environment}"

        print_status("Waiting for deployment to be ready...", "INFO")
        subprocess.run([
            "kubectl", "wait", "--for=condition=available",
            f"deployment/{deployment_name}", "-n", namespace, "--timeout=300s"
        ], check=True)

        print_status("Deployment completed successfully", "SUCCESS")
        return True

    except subprocess.CalledProcessError as e:
        print_status(f"Deployment failed: {e}", "ERROR")
        return False


def deploy_to_cluster(project_overlay, args):
    """Deploy to Kubernetes cluster"""
    if args.dry_run:
        print_status("Dry run mode - skipping deployment", "INFO")
        return True

    print_status(f"Deploying to {args.environment} cluster...", "INFO")

    try:
        # Apply manifests
        subprocess.run(["kubectl", "apply", "-k", str(project_overlay)], check=True)

        # Wait for deployment
        deployment_name = f"{args.project_id}-app"
        namespace = f"{args.project_id}-{args.environment}"

        print_status("Waiting for deployment to be ready...", "INFO")
        subprocess.run([
            "kubectl", "wait", "--for=condition=available",
            f"deployment/{deployment_name}", "-n", namespace, "--timeout=300s"
        ], check=True)

        print_status("Deployment completed successfully", "SUCCESS")
        return True

    except subprocess.CalledProcessError as e:
        print_status(f"Deployment failed: {e}", "ERROR")
        return False


def main():
    # Basic logging setup
    import logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

    parser = argparse.ArgumentParser(description="Unified cross-platform Kustomize deployment")

    # GitHub Actions payload mode
    parser.add_argument("--payload", help="GitHub Actions payload JSON")
    parser.add_argument("--secrets-encoded", help="Base64-encoded secrets JSON")

    # Direct parameters mode (backward compatibility)
    parser.add_argument("--project-id", help="Project identifier")
    parser.add_argument("--app-name", help="Application display name")
    parser.add_argument("--environment",
                       choices=["dev", "staging", "production"], help="Target environment")
    parser.add_argument("--container-image", help="Container image with tag")
    parser.add_argument("--app-type",
                       choices=["react-frontend", "django-backend", "nest-backend",
                               "springboot-backend", "web-app"], help="Application type")

    # Optional parameters
    parser.add_argument("--manifest-dir", default="manifests", help="Source manifest directory")
    parser.add_argument("--output-dir", default="generated-manifests", help="Output directory")
    parser.add_argument("--dry-run", action="store_true", help="Generate manifests only")
    parser.add_argument("--skip-validation", action="store_true", help="Skip all manifest validation")

    # Secret parameters (backward compatibility)
    parser.add_argument("--smtp-user", help="SMTP username")
    parser.add_argument("--smtp-pass", help="SMTP password")
    parser.add_argument("--google-client-id", help="Google OAuth client ID")
    parser.add_argument("--google-client-secret", help="Google OAuth client secret")

    args = parser.parse_args()

    print("Unified Cross-Platform Kustomize Deployment")
    print("=" * 50)

    # Debug: Show what arguments were received
    print_status("Script started with arguments:", "INFO")
    if args.payload:
        print_status(f"  Payload mode: {len(args.payload)} characters", "INFO")
        payload_preview = args.payload[:200] + "..." if len(args.payload) > 200 else args.payload
        print_status(f"  Payload preview: {payload_preview}", "INFO")
    if args.project_id:
        print_status(f"  Direct mode: project_id={args.project_id}", "INFO")
    if args.secrets_encoded:
        print_status(f"  Secrets provided: {len(args.secrets_encoded)} characters", "INFO")
    print_status(f"  Output dir: {args.output_dir}", "INFO")
    print_status(f"  Dry run: {args.dry_run}", "INFO")

    # Detect platform
    platform_info = detect_platform()

    # Check prerequisites
    if not check_prerequisites(platform_info):
        sys.exit(1)

    # Determine deployment mode
    if args.payload:
        print_status("Using GitHub Actions payload mode", "INFO")
        deployment_args = parse_github_payload(args)
        if not deployment_args:
            sys.exit(1)
    else:
        print_status("Using direct parameters mode", "INFO")
        deployment_args = args
        if not validate_inputs(deployment_args):
            sys.exit(1)
    
    # Create temporary directory for intermediate files
    with tempfile.TemporaryDirectory() as temp_dir:
        try:
            # Process payload and replace placeholders if using payload mode
            if args.payload:
                processed_manifests = process_payload_and_manifests(deployment_args, temp_dir)
                if not processed_manifests:
                    sys.exit(1)

                # Build and validate processed manifests
                output_file = build_and_validate_manifests_from_processed(
                    processed_manifests, deployment_args, deployment_args.output_dir
                )
                if not output_file:
                    sys.exit(1)

                # Copy processed manifests to final project directory
                if not copy_processed_manifests_to_project_dir(processed_manifests, deployment_args):
                    sys.exit(1)

                # Deploy processed manifests (only if not dry run)
                if not deployment_args.dry_run:
                    if not deploy_processed_manifests(processed_manifests, deployment_args):
                        sys.exit(1)
            else:
                # Legacy mode: Generate dynamic secrets and use old process
                secret_patch_file = generate_dynamic_secrets(deployment_args, temp_dir)
                if not secret_patch_file:
                    sys.exit(1)

                # Create project overlay
                project_overlay = create_project_overlay(deployment_args, platform_info, secret_patch_file)

                # Customize Kustomize configuration
                if not customize_kustomize_config(project_overlay, deployment_args, platform_info):
                    sys.exit(1)

                # Build and validate manifests
                output_file = build_and_validate_manifests(project_overlay, deployment_args, deployment_args.output_dir)
                if not output_file:
                    sys.exit(1)

                # Deploy to cluster
                if not deploy_to_cluster(project_overlay, deployment_args):
                    sys.exit(1)
            
            # Success summary
            print("\n" + "=" * 50)
            print_status("Deployment completed successfully!", "SUCCESS")
            print(f"\nSummary:")
            print(f"  Project: {args.app_name} ({args.project_id})")
            print(f"  Environment: {args.environment}")
            print(f"  App Type: {args.app_type}")
            print(f"  Platform: {platform_info['system']}")
            print(f"  Generated Manifests: {output_file}")
            
            if not args.dry_run:
                print(f"\n🔗 Next Steps:")
                print(f"  kubectl get pods -n {args.project_id}-{args.environment}")
                print(f"  kubectl logs -n {args.project_id}-{args.environment} -l app={args.project_id}")
            
        except Exception as e:
            print_status(f"Deployment failed: {e}", "ERROR")
            sys.exit(1)


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print_status("Deployment interrupted by user", "WARNING")
        sys.exit(1)
    except Exception as e:
        print_status(f"Unexpected error: {e}", "ERROR")
        import traceback
        traceback.print_exc()
        sys.exit(1)