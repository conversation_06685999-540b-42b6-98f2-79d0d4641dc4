# Environment Promotion Guide

This guide explains how to promote Docker images between environments (dev → staging → production) using GitOps principles with ArgoCD.

## Overview

The promotion workflow allows you to safely promote a Docker image that's currently deployed in one environment to the next environment in the promotion pipeline:

- **dev → staging**: Promote tested features to staging environment
- **staging → production**: Promote validated releases to production

## Promotion Methods

### 1. Automated Promotion (Recommended)

#### Using GitHub Actions Workflow

**Via GitHub UI:**
1. Go to the Actions tab in your GitOps repository
2. Select "🚀 Promote Between Environments" workflow
3. Click "Run workflow"
4. Fill in the parameters:
   - **Project ID**: `ai-spring-backend-sample`
   - **Source Environment**: `dev` or `staging`
   - **Target Environment**: `staging` or `production`
   - **Dry Run**: Check to preview without executing

**Via GitHub CLI:**
```bash
gh workflow run promote-environment.yaml \
  -f project_id=ai-spring-backend-sample \
  -f source_environment=dev \
  -f target_environment=staging \
  -f dry_run=false
```

**Via Repository Dispatch API:**
```bash
curl -X POST \
  -H "Authorization: token YOUR_GITHUB_TOKEN" \
  -H "Accept: application/vnd.github.v3+json" \
  https://api.github.com/repos/ChidhagniConsulting/gitops-argocd-apps/dispatches \
  -d '{
    "event_type": "promote-environment",
    "client_payload": {
      "project_id": "ai-spring-backend-sample",
      "source_environment": "dev",
      "target_environment": "staging",
      "dry_run": false
    }
  }'
```

#### Using Python Promotion Script

```bash
# Promote from dev to staging
python3 scripts/promote-image.py \
  --project-id ai-spring-backend-sample \
  --source-env dev \
  --target-env staging

# Promote from staging to production
python3 scripts/promote-image.py \
  --project-id ai-spring-backend-sample \
  --source-env staging \
  --target-env production

# Dry run to see what would happen
python3 scripts/promote-image.py \
  --project-id ai-spring-backend-sample \
  --source-env dev \
  --target-env staging \
  --dry-run

# Generate manual commands instead of executing
python3 scripts/promote-image.py \
  --project-id ai-spring-backend-sample \
  --source-env dev \
  --target-env staging \
  --manual
```

### 2. Manual Promotion

#### Using Shell Script Helper

```bash
# Make script executable (Linux/Mac)
chmod +x scripts/manual-promote.sh

# Promote with interactive guidance
./scripts/manual-promote.sh \
  --project-id ai-spring-backend-sample \
  --target-env staging

# Dry run to see commands
./scripts/manual-promote.sh \
  --project-id ai-spring-backend-sample \
  --source-env dev \
  --target-env staging \
  --dry-run
```

#### Direct ArgoCD Commands

```bash
# 1. First, generate manifests using repository dispatch
curl -X POST \
  -H "Authorization: token YOUR_GITHUB_TOKEN" \
  -H "Accept: application/vnd.github.v3+json" \
  https://api.github.com/repos/ChidhagniConsulting/gitops-argocd-apps/dispatches \
  -d '{
    "event_type": "deploy-to-argocd",
    "client_payload": {
      "app_name": "AI Spring Backend Sample",
      "project_id": "ai-spring-backend-sample",
      "application_type": "springboot-backend",
      "environment": "staging",
      "docker_image": "registry.digitalocean.com/doks-registry/ai-spring-backend",
      "docker_tag": "latest",
      "source_repo": "ChidhagniConsulting/ai-spring-backend",
      "source_branch": "main",
      "commit_sha": "promoted"
    }
  }'

# 2. Then apply the generated manifests
kubectl apply -f deployments/ai-spring-backend-sample/argocd/project.yaml
kubectl apply -f deployments/ai-spring-backend-sample/overlays/staging/application.yaml
```

## Validation and Safety

### Pre-Promotion Validation

Run validation checks before promoting:

```bash
# Check if promotion is safe
python3 scripts/validate-promotion.py \
  --project-id ai-spring-backend-sample \
  --source-env dev \
  --target-env staging \
  --source-image registry.digitalocean.com/doks-registry/ai-spring-backend:latest \
  --pre-promotion-check
```

### Health Monitoring

Check deployment health after promotion:

```bash
# Check application health
python3 scripts/validate-promotion.py \
  --project-id ai-spring-backend-sample \
  --target-env staging \
  --health-check

# Monitor ArgoCD application
kubectl get application ai-spring-backend-sample-staging -n argocd
kubectl describe application ai-spring-backend-sample-staging -n argocd

# Check pod status
kubectl get pods -n ai-spring-backend-sample-staging
kubectl logs -n ai-spring-backend-sample-staging deployment/ai-spring-backend-sample
```

### Rollback Planning

Create rollback plans before promotion:

```bash
# Create rollback plan
python3 scripts/validate-promotion.py \
  --project-id ai-spring-backend-sample \
  --target-env staging \
  --source-image registry.digitalocean.com/doks-registry/ai-spring-backend:previous-tag \
  --create-rollback-plan
```

## Current Deployment Status

To see what's currently deployed in each environment:

```bash
# Check dev environment
cat deployments/ai-spring-backend-sample/overlays/dev/patch-image.yaml

# Check staging environment  
cat deployments/ai-spring-backend-sample/overlays/staging/patch-image.yaml

# Check production environment
cat deployments/ai-spring-backend-sample/overlays/production/patch-image.yaml
```

## Example: Complete Promotion Workflow

Here's a complete example of promoting from dev to staging:

```bash
# 1. Check current dev deployment
echo "Current dev image:"
grep "image:" deployments/ai-spring-backend-sample/overlays/dev/patch-image.yaml

# 2. Validate promotion
python3 scripts/validate-promotion.py \
  --project-id ai-spring-backend-sample \
  --source-env dev \
  --target-env staging \
  --source-image registry.digitalocean.com/doks-registry/ai-spring-backend:latest \
  --pre-promotion-check

# 3. Create rollback plan
python3 scripts/validate-promotion.py \
  --project-id ai-spring-backend-sample \
  --target-env staging \
  --source-image $(grep "image:" deployments/ai-spring-backend-sample/overlays/staging/patch-image.yaml | awk '{print $2}') \
  --create-rollback-plan

# 4. Execute promotion
python3 scripts/promote-image.py \
  --project-id ai-spring-backend-sample \
  --source-env dev \
  --target-env staging

# 5. Monitor deployment
kubectl get application ai-spring-backend-sample-staging -n argocd -w

# 6. Verify health
python3 scripts/validate-promotion.py \
  --project-id ai-spring-backend-sample \
  --target-env staging \
  --health-check
```

## Troubleshooting

### Common Issues

1. **"Image not found" errors**
   - Verify the source image exists and is accessible
   - Check Docker registry credentials

2. **"Target environment not configured"**
   - Ensure target environment overlay files exist
   - Verify YAML syntax in target files

3. **ArgoCD application not syncing**
   - Check ArgoCD application status
   - Verify repository permissions
   - Check for syntax errors in manifests

4. **Promotion validation fails**
   - Review validation error messages
   - Check source environment health
   - Verify target environment configuration

### Debug Commands

```bash
# Check ArgoCD application logs
kubectl logs -n argocd deployment/argocd-application-controller

# Check ArgoCD server logs
kubectl logs -n argocd deployment/argocd-server

# Manually sync ArgoCD application
kubectl patch application ai-spring-backend-sample-staging -n argocd \
  --type merge -p '{"operation":{"sync":{}}}'

# Force refresh ArgoCD application
kubectl annotate application ai-spring-backend-sample-staging -n argocd \
  argocd.argoproj.io/refresh=normal
```

## Security Considerations

1. **GitHub Token Permissions**: Ensure your GitHub token has appropriate repository and workflow permissions
2. **Image Registry Access**: Verify that the target cluster can pull from your Docker registry
3. **Environment Isolation**: Ensure proper network policies and RBAC are in place
4. **Approval Gates**: Consider implementing approval requirements for production promotions

## Integration with CI/CD

The promotion workflow integrates seamlessly with your existing CI/CD pipeline:

1. **Development**: CI/CD deploys to dev environment automatically
2. **Staging**: Use promotion workflow to move tested features to staging
3. **Production**: Use promotion workflow with additional approvals for production

This ensures a controlled, auditable path from development to production while maintaining GitOps principles.
