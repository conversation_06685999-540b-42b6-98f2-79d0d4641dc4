apiVersion: v1
kind: Pod
metadata:
  name: database-debug-pod
  namespace: ai-spring-backend-sample-dev
  labels:
    app: database-debug
    purpose: troubleshooting
spec:
  restartPolicy: Never
  containers:
  - name: debug-container
    image: postgres:13-alpine
    command: ['sh', '-c']
    args:
    - |
      echo "🔍 Database Connectivity Debug Script"
      echo "===================================="
      
      # Extract database connection details from environment variables
      DB_HOST_DECODED=$(echo "$DB_HOST_B64" | base64 -d)
      DB_PORT_DECODED=$(echo "$DB_PORT_B64" | base64 -d)
      DB_USER_DECODED=$(echo "$DB_USER_B64" | base64 -d)
      DB_NAME_DECODED=$(echo "$DB_NAME_B64" | base64 -d)
      
      echo "📋 Database Configuration:"
      echo "  Host: $DB_HOST_DECODED"
      echo "  Port: $DB_PORT_DECODED"
      echo "  User: $DB_USER_DECODED"
      echo "  Database: $DB_NAME_DECODED"
      echo "  SSL Mode: $PGSSLMODE"
      echo ""
      
      echo "🌐 Network Connectivity Tests:"
      echo "------------------------------"
      
      # Test DNS resolution
      echo "1. Testing DNS resolution..."
      if nslookup "$DB_HOST_DECODED"; then
        echo "   ✅ DNS resolution successful"
      else
        echo "   ❌ DNS resolution failed"
      fi
      echo ""
      
      # Test network connectivity (ping)
      echo "2. Testing network connectivity (ping)..."
      if ping -c 3 "$DB_HOST_DECODED"; then
        echo "   ✅ Network connectivity successful"
      else
        echo "   ❌ Network connectivity failed"
      fi
      echo ""
      
      # Test port connectivity (telnet alternative)
      echo "3. Testing port connectivity..."
      if timeout 10 sh -c "echo > /dev/tcp/$DB_HOST_DECODED/$DB_PORT_DECODED" 2>/dev/null; then
        echo "   ✅ Port $DB_PORT_DECODED is accessible"
      else
        echo "   ❌ Port $DB_PORT_DECODED is not accessible"
      fi
      echo ""
      
      # Test PostgreSQL connectivity
      echo "4. Testing PostgreSQL connectivity..."
      if pg_isready -h "$DB_HOST_DECODED" -p "$DB_PORT_DECODED" -U "$DB_USER_DECODED"; then
        echo "   ✅ PostgreSQL server is ready"
      else
        echo "   ❌ PostgreSQL server is not ready"
      fi
      echo ""
      
      # Test database connection
      echo "5. Testing database connection..."
      if psql -h "$DB_HOST_DECODED" -p "$DB_PORT_DECODED" -U "$DB_USER_DECODED" -d "$DB_NAME_DECODED" -c "SELECT 1;" > /dev/null 2>&1; then
        echo "   ✅ Database connection successful"
      else
        echo "   ❌ Database connection failed"
        echo "   Attempting to get more details..."
        psql -h "$DB_HOST_DECODED" -p "$DB_PORT_DECODED" -U "$DB_USER_DECODED" -d "$DB_NAME_DECODED" -c "SELECT 1;" 2>&1 || true
      fi
      echo ""
      
      echo "🔧 Environment Variables:"
      echo "------------------------"
      env | grep -E "(DB_|PG)" | sort
      echo ""
      
      echo "🌍 Network Information:"
      echo "----------------------"
      echo "Pod IP: $(hostname -i)"
      echo "Hostname: $(hostname)"
      echo ""
      
      echo "📊 Debug Summary:"
      echo "=================="
      echo "If all tests pass, the database connectivity is working correctly."
      echo "If any tests fail, check the corresponding network/security configurations."
      echo ""
      echo "Common issues:"
      echo "- Database server not accessible from Kubernetes cluster"
      echo "- Firewall blocking connections"
      echo "- Incorrect credentials"
      echo "- SSL/TLS configuration issues"
      echo ""
      
      # Keep the pod running for manual inspection
      echo "⏳ Keeping pod alive for 300 seconds for manual inspection..."
      sleep 300
    env:
    # Database connection details from secrets
    - name: DB_HOST_B64
      valueFrom:
        secretKeyRef:
          name: ai-spring-backend-sample-secrets
          key: DB_HOST
    - name: DB_PORT_B64
      valueFrom:
        secretKeyRef:
          name: ai-spring-backend-sample-secrets
          key: DB_PORT
    - name: DB_USER_B64
      valueFrom:
        secretKeyRef:
          name: ai-spring-backend-sample-secrets
          key: DB_USER
    - name: DB_NAME_B64
      valueFrom:
        secretKeyRef:
          name: ai-spring-backend-sample-secrets
          key: DB_NAME
    - name: PGPASSWORD
      valueFrom:
        secretKeyRef:
          name: ai-spring-backend-sample-secrets
          key: DB_PASSWORD
    - name: PGSSLMODE
      valueFrom:
        secretKeyRef:
          name: ai-spring-backend-sample-secrets
          key: DB_SSL_MODE
