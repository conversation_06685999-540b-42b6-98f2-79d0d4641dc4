apiVersion: v1
kind: ConfigMap
metadata:
  name: app-env-config
data:
  env-config.js: |
    window.ENV = {
      // Application Configuration
      APP_NAME: "PLACEHOLDER_APP_NAME",
      APP_VERSION: "PLACEH<PERSON>DER_COMMIT_SHA",
      ENVIRONMENT: "PLACEHOLDER_ENVIRONMENT",
      
      // API Configuration
      API_BASE_URL: "PLACEHOLDER_API_BASE_URL",
      API_TIMEOUT: 30000,
      
      // Feature Flags
      ENABLE_ANALYTICS: PLACEHOLDER_ENABLE_ANALYTICS,
      ENABLE_DEBUG: PLACEHOLDER_ENABLE_DEBUG,
      
      // Backend Configuration
      BACKEND_TYPE: "PLACEHOLDER_BACKEND_TYPE",
      BACKEND_URL: "PLACEHOLDER_BACKEND_URL",
      
      // Build Information
      BUILD_TIME: "PLACEHOLDER_BUILD_TIME",
      COMMIT_SHA: "PLACEHOLDER_COMMIT_SHA",
      SOURCE_BRANCH: "PLACEH<PERSON>DER_SOURCE_BRANCH"
    };
