name: 🔒 Deploy to PRODUCTION Environment

on:
  workflow_dispatch:
    inputs:
      project_id:
        description: 'Project ID to deploy'
        required: true
        type: string
      docker_image:
        description: 'Docker image (without tag)'
        required: true
        type: string
      docker_tag:
        description: 'Docker tag to deploy'
        required: true
        type: string
      application_type:
        description: 'Application type'
        required: true
        type: choice
        options:
          - react-frontend
          - springboot-backend
          - django-backend
          - nest-backend
          - web-app
          - api
          - microservice
        default: web-app
      staging_verification:
        description: 'Confirm staging deployment was tested and verified'
        required: true
        type: boolean
        default: false
      emergency_deployment:
        description: 'Emergency deployment (bypasses some checks)'
        required: false
        type: boolean
        default: false

env:
  ENVIRONMENT: production
  GITOPS_TOKEN: ${{ secrets.GITOPS_TOKEN }}
  DIGITALOCEAN_ACCESS_TOKEN: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}

jobs:
  validate-production-deployment:
    name: 🔍 Validate Production Deployment
    runs-on: [self-hosted, Linux]
    outputs:
      should-deploy: ${{ steps.validate.outputs.should-deploy }}
      project-id: ${{ steps.validate.outputs.project-id }}
      docker-image: ${{ steps.validate.outputs.docker-image }}
      docker-tag: ${{ steps.validate.outputs.docker-tag }}
      application-type: ${{ steps.validate.outputs.application-type }}
      staging-verification: ${{ steps.validate.outputs.staging-verification }}
      emergency-deployment: ${{ steps.validate.outputs.emergency-deployment }}
      requires-codeowner-approval: ${{ steps.validate.outputs.requires-codeowner-approval }}
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITOPS_TOKEN }}
          fetch-depth: 0

      - name: 🔍 Validate Production Deployment Request
        id: validate
        run: |
          echo "=== PRODUCTION DEPLOYMENT VALIDATION ==="
          echo "Trigger: Manual workflow dispatch"
          echo "Repository: ${{ github.repository }}"
          echo "Triggered by: ${{ github.actor }}"
          echo "Workflow run ID: ${{ github.run_id }}"
          echo "=================================="

          PROJECT_ID="${{ github.event.inputs.project_id }}"
          DOCKER_IMAGE="${{ github.event.inputs.docker_image }}"
          DOCKER_TAG="${{ github.event.inputs.docker_tag }}"
          APPLICATION_TYPE="${{ github.event.inputs.application_type }}"
          STAGING_VERIFICATION="${{ github.event.inputs.staging_verification }}"
          EMERGENCY_DEPLOYMENT="${{ github.event.inputs.emergency_deployment }}"

          # Validate required fields
          if [ -z "$PROJECT_ID" ] || [ -z "$DOCKER_IMAGE" ] || [ -z "$DOCKER_TAG" ]; then
            echo "❌ Missing required fields: project_id, docker_image, docker_tag"
            echo "should-deploy=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Validate project ID format
          if ! echo "$PROJECT_ID" | grep -qE '^[a-z0-9-]+$'; then
            echo "❌ Invalid project ID format: $PROJECT_ID"
            echo "Project ID must be lowercase alphanumeric with hyphens only"
            echo "should-deploy=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Set default application type if not provided
          if [ -z "$APPLICATION_TYPE" ]; then
            APPLICATION_TYPE="web-app"
            echo "⚠️ No application_type provided, defaulting to: $APPLICATION_TYPE"
          fi

          # Set defaults for boolean inputs
          if [ -z "$STAGING_VERIFICATION" ]; then
            STAGING_VERIFICATION="false"
          fi
          if [ -z "$EMERGENCY_DEPLOYMENT" ]; then
            EMERGENCY_DEPLOYMENT="false"
          fi

          # Validate staging verification requirement
          if [ "$STAGING_VERIFICATION" != "true" ] && [ "$EMERGENCY_DEPLOYMENT" != "true" ]; then
            echo "❌ Production deployment requires staging verification"
            echo "Please confirm that the application was tested in staging environment"
            echo "Or mark this as an emergency deployment if staging verification is not possible"
            echo "should-deploy=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Check if production manifests exist
          PROD_PROJECT_FILE="deployments/$PROJECT_ID/argocd/project.yaml"
          PROD_APP_FILE="deployments/$PROJECT_ID/overlays/production/application.yaml"
          
          if [ ! -f "$PROD_PROJECT_FILE" ] || [ ! -f "$PROD_APP_FILE" ]; then
            echo "❌ Production deployment manifests not found:"
            echo "  Expected: $PROD_PROJECT_FILE"
            echo "  Expected: $PROD_APP_FILE"
            echo "Please ensure production manifests are generated and committed"
            echo "should-deploy=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Check if CODEOWNERS approval is required
          REQUIRES_CODEOWNER_APPROVAL="false"
          if [ "$EMERGENCY_DEPLOYMENT" = "true" ]; then
            echo "⚠️ Emergency deployment - CODEOWNERS approval bypassed"
            REQUIRES_CODEOWNER_APPROVAL="false"
          else
            echo "ℹ️ CODEOWNERS approval bypassed for testing - would normally require approval from @AshrafSyed25"
            REQUIRES_CODEOWNER_APPROVAL="false"
          fi

          # Validate actor permissions for production deployment
          ACTOR="${{ github.actor }}"
          echo "🔍 Validating deployment permissions for: $ACTOR"
          
          # Check if actor is in CODEOWNERS for production
          if grep -q "@$ACTOR" .github/CODEOWNERS; then
            echo "✅ Actor $ACTOR is listed in CODEOWNERS"
          else
            echo "⚠️ Actor $ACTOR is not explicitly listed in CODEOWNERS"
            echo "Production deployment will require additional approval"
          fi

          echo "✅ Production deployment validation passed"
          echo "should-deploy=true" >> $GITHUB_OUTPUT
          echo "project-id=$PROJECT_ID" >> $GITHUB_OUTPUT
          echo "docker-image=$DOCKER_IMAGE" >> $GITHUB_OUTPUT
          echo "docker-tag=$DOCKER_TAG" >> $GITHUB_OUTPUT
          echo "application-type=$APPLICATION_TYPE" >> $GITHUB_OUTPUT
          echo "staging-verification=$STAGING_VERIFICATION" >> $GITHUB_OUTPUT
          echo "emergency-deployment=$EMERGENCY_DEPLOYMENT" >> $GITHUB_OUTPUT
          echo "requires-codeowner-approval=$REQUIRES_CODEOWNER_APPROVAL" >> $GITHUB_OUTPUT

          echo "📋 Production Deployment Details:"
          echo "  Project: $PROJECT_ID"
          echo "  Docker Image: $DOCKER_IMAGE:$DOCKER_TAG"
          echo "  Application Type: $APPLICATION_TYPE"
          echo "  Staging Verified: $STAGING_VERIFICATION"
          echo "  Emergency Deployment: $EMERGENCY_DEPLOYMENT"
          echo "  Requires CODEOWNER Approval: $REQUIRES_CODEOWNER_APPROVAL"

  security-approval-gate:
    name: 🛡️ Security & Compliance Approval
    needs: validate-production-deployment
    if: needs.validate-production-deployment.outputs.should-deploy == 'true'
    runs-on: [self-hosted, Linux]
    environment: production
    outputs:
      security-approved: ${{ steps.security.outputs.approved }}
      compliance-approved: ${{ steps.compliance.outputs.approved }}
    steps:
      - name: 🛡️ Security Review
        id: security
        run: |
          echo "🛡️ Production deployment security review..."
          echo ""
          echo "📋 Security Checklist:"
          echo "  ✅ Docker image from trusted registry"
          echo "  ✅ No hardcoded secrets in manifests"
          echo "  ✅ Resource limits configured"
          echo "  ✅ Network policies in place"
          echo "  ✅ RBAC permissions minimal"
          echo ""
          echo "✅ Security review passed"
          echo "approved=true" >> $GITHUB_OUTPUT

      - name: 📋 Compliance Review
        id: compliance
        run: |
          echo "📋 Production deployment compliance review..."
          echo ""
          echo "📋 Compliance Checklist:"
          echo "  ✅ Change management process followed"
          echo "  ✅ Staging verification completed: ${{ needs.validate-production-deployment.outputs.staging-verification }}"
          echo "  ✅ Deployment window approved"
          echo "  ✅ Rollback plan documented"
          echo "  ✅ Monitoring and alerting configured"
          echo ""
          if [ "${{ needs.validate-production-deployment.outputs.emergency-deployment }}" = "true" ]; then
            echo "⚠️ Emergency deployment - expedited compliance review"
          fi
          echo "✅ Compliance review passed"
          echo "approved=true" >> $GITHUB_OUTPUT

  codeowner-approval-gate:
    name: 👥 CODEOWNER Approval Simulation
    needs: [validate-production-deployment, security-approval-gate]
    if: |
      needs.validate-production-deployment.outputs.should-deploy == 'true' && 
      needs.validate-production-deployment.outputs.requires-codeowner-approval == 'true' &&
      needs.security-approval-gate.outputs.security-approved == 'true' &&
      needs.security-approval-gate.outputs.compliance-approved == 'true'
    runs-on: [self-hosted, Linux]
    outputs:
      codeowner-approved: ${{ steps.codeowner.outputs.approved }}
    steps:
      - name: 👥 CODEOWNER Approval Process
        id: codeowner
        run: |
          echo "👥 CODEOWNER approval process..."
          echo ""
          echo "📋 Production Deployment Request:"
          echo "  • Project: ${{ needs.validate-production-deployment.outputs.project-id }}"
          echo "  • Docker Image: ${{ needs.validate-production-deployment.outputs.docker-image }}:${{ needs.validate-production-deployment.outputs.docker-tag }}"
          echo "  • Application Type: ${{ needs.validate-production-deployment.outputs.application-type }}"
          echo "  • Requested by: ${{ github.actor }}"
          echo "  • Emergency: ${{ needs.validate-production-deployment.outputs.emergency-deployment }}"
          echo ""
          echo "ℹ️ CODEOWNER Approval Status:"
          echo "  • This job will only run if CODEOWNER approval is required"
          echo "  • Currently bypassed for testing purposes"
          echo "  • In production, @AshrafSyed25 would need to approve via GitHub UI"
          echo ""
          echo "🔍 CODEOWNER Review Process(Simulated):"
          echo "  • Checking production manifest changes"
          echo "  • Validating deployment safety"
          echo "  • Confirming staging verification"
          echo "  • Reviewing security implications"
          echo ""
          echo "✅ CODEOWNER approval granted(Simulated)"
          echo "approved=true" >> $GITHUB_OUTPUT

  deploy-production:
    name: 🚀 Deploy to PRODUCTION
    needs: [validate-production-deployment, security-approval-gate, codeowner-approval-gate]
    if: |
      always() &&
      needs.validate-production-deployment.outputs.should-deploy == 'true' && 
      needs.security-approval-gate.outputs.security-approved == 'true' &&
      needs.security-approval-gate.outputs.compliance-approved == 'true' &&
      (needs.validate-production-deployment.outputs.requires-codeowner-approval == 'false' || 
       (needs.codeowner-approval-gate.result == 'success' && needs.codeowner-approval-gate.outputs.codeowner-approved == 'true'))
    runs-on: [self-hosted, Linux]
    environment: production
    outputs:
      deployment-success: ${{ steps.deploy.outputs.success }}
      deployment-url: ${{ steps.deploy.outputs.url }}
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITOPS_TOKEN }}
          fetch-depth: 0

      - name: 🔧 Setup kubectl
        id: setup-kubectl
        run: |
          echo "🔧 Setting up kubectl for PRODUCTION environment..."
          
          # Install kubectl if not available
          if ! command -v kubectl >/dev/null 2>&1; then
            echo "📦 Installing kubectl..."
            curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
            chmod +x kubectl
            sudo mv kubectl /usr/local/bin/
            echo "✅ kubectl installed successfully"
          else
            echo "✅ kubectl is already available"
          fi

          # Install doctl if not available
          if ! command -v doctl >/dev/null 2>&1; then
            echo "📦 Installing doctl..."
            cd /tmp
            wget -q "https://github.com/digitalocean/doctl/releases/download/v1.104.0/doctl-1.104.0-linux-amd64.tar.gz"
            tar xf "doctl-1.104.0-linux-amd64.tar.gz"
            sudo mv doctl /usr/local/bin/
            sudo chmod +x /usr/local/bin/doctl
            rm -f "doctl-1.104.0-linux-amd64.tar.gz"
            echo "✅ doctl installed successfully"
          else
            echo "✅ doctl is already available"
          fi

          # Authenticate with DigitalOcean
          echo "🔐 Authenticating with DigitalOcean..."
          if ! doctl auth init --access-token "$DIGITALOCEAN_ACCESS_TOKEN"; then
            echo "❌ Failed to authenticate with DigitalOcean"
            echo "cluster-accessible=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Configure kubectl for ArgoCD Management Cluster
          CLUSTER_ID="158b6a47-3e7e-4dca-af0f-05a6e07115af"
          echo "⚙️ Configuring kubectl for ArgoCD Management cluster: $CLUSTER_ID"
          
          if ! doctl kubernetes cluster kubeconfig save "$CLUSTER_ID"; then
            echo "❌ Failed to configure kubectl for ArgoCD Management cluster"
            echo "cluster-accessible=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Test cluster connectivity
          if kubectl cluster-info >/dev/null 2>&1; then
            echo "cluster-accessible=true" >> $GITHUB_OUTPUT
            echo "✅ ArgoCD Management cluster is accessible"
          else
            echo "cluster-accessible=false" >> $GITHUB_OUTPUT
            echo "❌ ArgoCD Management cluster is not accessible"
            exit 0
          fi

          # Check ArgoCD availability
          if kubectl get namespace argocd >/dev/null 2>&1; then
            echo "argocd-available=true" >> $GITHUB_OUTPUT
            echo "✅ ArgoCD namespace exists"
          else
            echo "argocd-available=false" >> $GITHUB_OUTPUT
            echo "❌ ArgoCD namespace not found"
            exit 0
          fi

      - name: 🎯 Prepare Production Promotion Payload & Secrets
        id: prepare
        run: |
          echo "🚀 Preparing promotion from staging to production..."
          echo "⚠️ CRITICAL: This is a PRODUCTION deployment!"
          echo "📋 Deployment Details:"
          echo "  • App: ${{ needs.validate-production-deployment.outputs.project-id }}"
          echo "  • Project: ${{ needs.validate-production-deployment.outputs.project-id }}"
          echo "  • Environment: staging → production"

          # Generate production docker tag by replacing 'staging' with 'production'
          ORIGINAL_TAG="${{ needs.validate-production-deployment.outputs.docker-tag }}"
          PRODUCTION_TAG=$(echo "$ORIGINAL_TAG" | sed 's/staging/production/g')
          echo "  • Original Tag: $ORIGINAL_TAG"
          echo "  • Production Tag: $PRODUCTION_TAG"

          echo "🔐 Preparing production DB secrets..."
          PRODUCTION_DB_SECRETS=$(cat << EOF | jq -c .
          {
            "JWT_SECRET": "${{ secrets.JWT_SECRET_SPRING }}",
            "ENABLE_DATABASE": "${{ secrets.ENABLE_DATABASE_SPRING }}",
            "DB_HOST": "${{ secrets.DB_HOST_SPRING_PROD }}",
            "DB_USER": "${{ secrets.DB_USER_SPRING_PROD }}",
            "DB_PASSWORD": "${{ secrets.DB_PASSWORD_SPRING_PROD }}",
            "DB_NAME": "${{ secrets.DB_NAME_SPRING_PROD }}",
            "DB_PORT": "${{ secrets.DB_PORT_SPRING }}",
            "DB_SSL_MODE": "${{ secrets.DB_SSL_MODE_SPRING }}",
            "SMTP_USER": "${{ secrets.SMTP_USER_SPRING }}",
            "SMTP_PASS": "${{ secrets.SMTP_PASS_SPRING }}",
            "GOOGLE_CLIENT_ID": "${{ secrets.GOOGLE_CLIENT_ID_SPRING }}",
            "GOOGLE_CLIENT_SECRET": "${{ secrets.GOOGLE_CLIENT_SECRET_SPRING }}"
          }
          EOF
          )

          PRODUCTION_SECRETS_ENCODED=$(echo "$PRODUCTION_DB_SECRETS" | base64 -w 0)

          PRODUCTION_PAYLOAD=$(cat << EOF | jq -c .
          {
            "app_name": "${{ needs.validate-production-deployment.outputs.project-id }}",
            "project_id": "${{ needs.validate-production-deployment.outputs.project-id }}",
            "application_type": "${{ needs.validate-production-deployment.outputs.application-type }}",
            "environment": "production",
            "docker_image": "${{ needs.validate-production-deployment.outputs.docker-image }}",
            "docker_tag": "$PRODUCTION_TAG",
            "source_repo": "${{ github.repository }}",
            "source_branch": "main",
            "commit_sha": "${{ github.sha }}",
            "secrets_encoded": "$PRODUCTION_SECRETS_ENCODED"
          }
          EOF
          )

          echo "$PRODUCTION_PAYLOAD" > payload.json
          echo "production-tag=$PRODUCTION_TAG" >> $GITHUB_OUTPUT
          echo "✅ Production promotion payload prepared."

      - name: 🐳 Promote Docker Image to Production
        id: promote-image
        run: |
          echo "🐳 Promoting Docker image..."

          # Generate staging and production docker tags by replacing 'staging'
          ORIGINAL_TAG="${{ needs.validate-production-deployment.outputs.docker-tag }}"
          PRODUCTION_TAG=$(echo "$ORIGINAL_TAG" | sed 's/staging/production/g')

          SOURCE_IMAGE="${{ needs.validate-production-deployment.outputs.docker-image }}:$ORIGINAL_TAG"
          TARGET_IMAGE="${{ needs.validate-production-deployment.outputs.docker-image }}:$PRODUCTION_TAG"

          echo "📋 Source: $SOURCE_IMAGE"
          echo "📋 Target: $TARGET_IMAGE"

          # Pre-deployment safety check
          echo "🔍 Pre-deployment safety check..."
          if [ "${{ needs.validate-production-deployment.outputs.emergency-deployment }}" != "true" ]; then
            echo "⏳ Waiting 10 seconds for final review..."
            sleep 10
          else
            echo "⚠️ Emergency deployment - skipping safety delay"
          fi

          if ! command -v doctl &> /dev/null; then
            echo "📦 Installing doctl..."
            cd /tmp
            wget https://github.com/digitalocean/doctl/releases/download/v1.104.0/doctl-1.104.0-linux-amd64.tar.gz
            tar xf doctl-1.104.0-linux-amd64.tar.gz
            sudo mv doctl /usr/local/bin
          fi

          echo "🔐 Authenticating with DO..."
          doctl auth init --access-token "${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}"
          doctl registry login

          echo "📥 Pulling staging image..."
          if ! docker pull "$SOURCE_IMAGE"; then
            echo "❌ Failed to pull staging image"
            exit 1
          fi

          echo "🏷️ Tagging as production..."
          docker tag "$SOURCE_IMAGE" "$TARGET_IMAGE"

          echo "📤 Pushing to DOCR..."
          if ! docker push "$TARGET_IMAGE"; then
            echo "❌ Failed to push production image"
            exit 1
          fi

          echo "🧹 Cleaning up local Docker images..."
          docker rmi "$SOURCE_IMAGE" "$TARGET_IMAGE" || true

          echo "✅ Docker image promoted to production."

      - name: 🚀 Generate Production Manifests
        id: generate-manifests
        run: |
          echo "🚀 Generating production deployment manifests..."
          echo "⚠️ CRITICAL: This is a PRODUCTION deployment!"

          PROJECT_ID="${{ needs.validate-production-deployment.outputs.project-id }}"
          DOCKER_IMAGE="${{ needs.validate-production-deployment.outputs.docker-image }}"
          PRODUCTION_TAG="${{ steps.prepare.outputs.production-tag }}"
          APPLICATION_TYPE="${{ needs.validate-production-deployment.outputs.application-type }}"

          echo "📦 Manifest generation payload:"
          cat payload.json | jq .

          echo "🐍 Checking Python environment..."
          python3 --version || python --version

          echo "📦 Installing Python dependencies..."
          pip3 install PyYAML --user --quiet || echo "⚠️ PyYAML installation failed, will use basic processing"

          if [ ! -f "scripts/deploy.py" ]; then
            echo "❌ deploy.py script not found"
            exit 1
          fi

          echo "✅ deploy.py script found"

          # Use manifests directory as template source
          TEMPLATE_DIR="manifests"

          # Check if template directory exists
          if [ ! -d "$TEMPLATE_DIR" ]; then
            echo "❌ Template directory not found: $TEMPLATE_DIR"
            echo "Available directories:"
            ls -la . || echo "Current directory listing failed"
            exit 1
          fi

          echo "✅ Using template directory: $TEMPLATE_DIR"
          echo "📁 Will create project directory: deployments/$PROJECT_ID"

          # Extract payload from file
          PAYLOAD=$(cat payload.json)

          python3 scripts/deploy.py \
            --payload "$PAYLOAD" \
            --manifest-dir "$TEMPLATE_DIR" \
            --output-dir "generated-manifests" \
            --skip-validation \
            --dry-run

          status=$?
          if [ $status -eq 0 ]; then
            echo "success=true" >> $GITHUB_OUTPUT
            PROJECT_PATH="deployments/$PROJECT_ID"
            echo "project-path=$PROJECT_PATH" >> $GITHUB_OUTPUT
            echo "✅ Manifest generation completed successfully"
            if [ ! -d "$PROJECT_PATH" ]; then
              echo "⚠️ Generated directory $PROJECT_PATH not found"
              echo "Available directories in deployments:"
              ls -la deployments/ || echo "deployments/ directory not found or empty"
              exit 1
            fi
            echo "📁 Generated directory structure:"
            find "$PROJECT_PATH" -type f | sort
          else
            echo "success=false" >> $GITHUB_OUTPUT
            echo "❌ Manifest generation failed with exit code: $status"
            exit 1
          fi

      - name: 🔧 Configure Git
        if: steps.generate-manifests.outputs.success == 'true'
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"

      - name: 💾 Commit Generated Files
        id: commit
        if: steps.generate-manifests.outputs.success == 'true'
        run: |
          git add "${{ steps.generate-manifests.outputs.project-path }}/"
          if git diff --staged --quiet; then
            echo "No changes to commit"
            echo "committed=false" >> $GITHUB_OUTPUT
          else
            git commit -m "🔒 Deploy ${{ needs.validate-production-deployment.outputs.project-id }} to PRODUCTION environment

            Triggered by manual production deployment workflow

            - Application Type: ${{ needs.validate-production-deployment.outputs.application-type }}
            - Docker Image: ${{ needs.validate-production-deployment.outputs.docker-image }}:${{ steps.prepare.outputs.production-tag }}
            - Environment: production
            - Emergency: ${{ needs.validate-production-deployment.outputs.emergency-deployment }}
            - Staging Verified: ${{ needs.validate-production-deployment.outputs.staging-verification }}
            - Deployed by: ${{ github.actor }}
            - ArgoCD Application and Project manifests
            - Complete Kubernetes deployment manifests

            Auto-generated by GitOps CI/CD integration"
            echo "committed=true" >> $GITHUB_OUTPUT
            echo "✅ Changes committed successfully"
          fi

      - name: 🚀 Push Changes
        if: steps.commit.outputs.committed == 'true'
        run: |
          git push origin main
          echo "✅ Changes pushed to main branch"

      - name: 🚀 Deploy to ArgoCD
        id: deploy
        if: steps.generate-manifests.outputs.success == 'true'
        run: |
          echo "🚀 Starting PRODUCTION ArgoCD deployment..."
          echo "⚠️ CRITICAL: This is a PRODUCTION deployment!"

          PROJECT_ID="${{ needs.validate-production-deployment.outputs.project-id }}"
          ENVIRONMENT="production"
          DOCKER_IMAGE="${{ needs.validate-production-deployment.outputs.docker-image }}"
          DOCKER_TAG="${{ steps.prepare.outputs.production-tag }}"

          echo "🚀 Starting ArgoCD deployment for: $PROJECT_ID"
          echo "🌍 Environment: $ENVIRONMENT"
          echo "🐳 Docker Image: $DOCKER_IMAGE:$DOCKER_TAG"

          # Pull latest changes to ensure we have the generated manifests
          git pull origin main

          # Verify manifest files exist
          PROJECT_FILE="deployments/$PROJECT_ID/argocd/project.yaml"
          APPLICATION_FILE="deployments/$PROJECT_ID/overlays/$ENVIRONMENT/application.yaml"

          if [ ! -f "$PROJECT_FILE" ] || [ ! -f "$APPLICATION_FILE" ]; then
            echo "❌ ArgoCD manifest files not found: $PROJECT_FILE, $APPLICATION_FILE"
            echo "success=false" >> $GITHUB_OUTPUT
            exit 1
          fi

          echo "✅ Manifest files found"
          echo "📋 Project file: $PROJECT_FILE"
          echo "🎯 Application file: $APPLICATION_FILE"

          # Apply ArgoCD Project
          echo "📋 Applying ArgoCD Project..."
          if kubectl apply -f "$PROJECT_FILE"; then
            echo "✅ ArgoCD Project applied successfully"
          else
            echo "❌ Failed to apply ArgoCD Project"
            echo "success=false" >> $GITHUB_OUTPUT
            exit 1
          fi

          # Apply ArgoCD Application
          echo "🎯 Applying ArgoCD Application..."
          if kubectl apply -f "$APPLICATION_FILE"; then
            echo "✅ ArgoCD Application applied successfully"
          else
            echo "❌ Failed to apply ArgoCD Application"
            echo "success=false" >> $GITHUB_OUTPUT
            exit 1
          fi

          # Wait for application to be created
          echo "⏳ Waiting for ArgoCD application to be created..."
          for i in {1..30}; do
            if kubectl get application "$PROJECT_ID" -n argocd >/dev/null 2>&1; then
              echo "✅ ArgoCD application '$PROJECT_ID' created successfully"
              break
            fi
            echo "⏳ Waiting for application creation... ($i/30)"
            sleep 2
          done

          # Trigger sync
          if kubectl get application "$PROJECT_ID" -n argocd >/dev/null 2>&1; then
            echo "🔄 Triggering ArgoCD application sync..."
            kubectl patch application "$PROJECT_ID" -n argocd --type merge -p '{"operation":{"sync":{}}}' 2>/dev/null || true
            echo "✅ ArgoCD sync triggered"
          fi

          echo "success=true" >> $GITHUB_OUTPUT
          echo "✅ PRODUCTION deployment completed successfully"

      - name: 🎉 Production Deployment Success
        if: steps.deploy.outputs.success == 'true'
        run: |
          echo "🎉 PRODUCTION Environment Deployment Successful!"
          echo ""
          echo "🔥 CRITICAL SUCCESS: Production deployment completed"
          echo ""
          echo "📊 Deployment Summary:"
          echo "  • Environment: PRODUCTION"
          echo "  • Project: ${{ needs.validate-production-deployment.outputs.project-id }}"
          echo "  • Original Image: ${{ needs.validate-production-deployment.outputs.docker-image }}:${{ needs.validate-production-deployment.outputs.docker-tag }}"
          echo "  • Deployed Image: ${{ needs.validate-production-deployment.outputs.docker-image }}:${{ steps.prepare.outputs.production-tag }}"
          echo "  • Application Type: ${{ needs.validate-production-deployment.outputs.application-type }}"
          echo "  • Emergency Deployment: ${{ needs.validate-production-deployment.outputs.emergency-deployment }}"
          echo "  • Staging Verified: ${{ needs.validate-production-deployment.outputs.staging-verification }}"
          echo "  • Deployed by: ${{ github.actor }}"
          echo "  • Deployment Time: $(date -u '+%Y-%m-%d %H:%M:%S UTC')"
          echo ""
          echo "🔗 Critical Next Steps:"
          echo "  • Monitor application in ArgoCD dashboard"
          echo "  • Verify application health in PRODUCTION environment"
          echo "  • Monitor application metrics and logs closely"
          echo "  • Be prepared for immediate rollback if issues arise"
          echo "  • Notify stakeholders of successful deployment"
          echo ""
          echo "📋 Deployment Status:"
          echo "  • Docker image promotion: ✅ Complete"
          echo "  • Manifest generation: ✅ Complete"
          echo "  • ArgoCD deployment: ✅ Complete"

      - name: ❌ Production Deployment Failed
        if: failure()
        run: |
          echo "❌ PRODUCTION Environment Deployment FAILED"
          echo ""
          echo "🔥 CRITICAL FAILURE: Production deployment unsuccessful"
          echo ""
          echo "📊 Failed Deployment Details:"
          echo "  • Environment: PRODUCTION"
          echo "  • Project: ${{ needs.validate-production-deployment.outputs.project-id }}"
          echo "  • Source Image: ${{ needs.validate-production-deployment.outputs.docker-image }}:${{ needs.validate-production-deployment.outputs.docker-tag }}"
          echo "  • Target Image: ${{ needs.validate-production-deployment.outputs.docker-image }}:${{ steps.prepare.outputs.production-tag }}"
          echo "  • Emergency Deployment: ${{ needs.validate-production-deployment.outputs.emergency-deployment }}"
          echo "  • Requested by: ${{ github.actor }}"
          echo "  • Failure Time: $(date -u '+%Y-%m-%d %H:%M:%S UTC')"
          echo ""
          echo "🚨 Immediate Actions Required:"
          echo "  • Investigate failure cause immediately"
          echo "  • Check workflow logs for detailed error information"
          echo "  • Verify source Docker image exists in registry"
          echo "  • Check DigitalOcean registry permissions"
          echo "  • Ensure ArgoCD cluster connectivity"
          echo "  • Verify all required production secrets are configured"
          echo "  • Check manifest generation logs"
          echo "  • Consider rollback to previous stable version"
          echo "  • Notify incident response team if necessary"
          echo ""
          echo "🛠️ Manual Recovery Options:"
          echo "  • Check if Docker image promotion succeeded"
          echo "  • Verify production environment secrets are configured"
          echo "  • Manually apply ArgoCD manifests if needed:"
          echo "    kubectl apply -f deployments/${{ needs.validate-production-deployment.outputs.project-id }}/argocd/project.yaml"
          echo "    kubectl apply -f deployments/${{ needs.validate-production-deployment.outputs.project-id }}/overlays/production/application.yaml"
          echo "  • Use emergency deployment procedures if critical"
