name: 🚀 Application Deployment Request
description: Deploy a new application or update an existing one using GitOps automation
title: "[DEPLOY] "
labels: ["app-deployment"]
assignees: []
body:
  - type: markdown
    attributes:
      value: |
        ## 🚀 Application Deployment Request
        
        This form will automatically generate ArgoCD applications and Kubernetes manifests for your application deployment.
        
        **Please fill out all required fields carefully. The automation will create:**
        - Project directory structure (`{project-name}/argocd/` and `{project-name}/k8s/`)
        - ArgoCD Application manifest
        - Complete Kubernetes manifests (namespace, deployment, service, configmap, secret templates)
        - Optional PostgreSQL database setup
        - Optional ingress configuration

  - type: input
    id: app_name
    attributes:
      label: Application Name
      description: Name of your application (lowercase, alphanumeric, hyphens only)
      placeholder: "my-awesome-app"
    validations:
      required: true

  - type: input
    id: project_id
    attributes:
      label: Project Identifier
      description: Unique project identifier (will be used as directory name and namespace)
      placeholder: "my-awesome-app"
    validations:
      required: true

  - type: input
    id: container_image
    attributes:
      label: Container Image
      description: Full container image path (registry/repository:tag)
      placeholder: "docker.io/myorg/my-app:v1.0.0"
    validations:
      required: true

  - type: dropdown
    id: environment
    attributes:
      label: Environment
      description: Target deployment environment
      options:
        - dev
        - staging
        - prod
      default: 0
    validations:
      required: true

  - type: dropdown
    id: target_cluster
    attributes:
      label: Target Cluster
      description: Select the cluster where the application should be deployed
      options:
        - dev-staging (6be4e15d-52f9-431d-84ec-ec8cad0dff2d)
        - production (158b6a47-3e7e-4dca-af0f-05a6e07115af)
      default: 0
    validations:
      required: true

  - type: input
    id: namespace
    attributes:
      label: Kubernetes Namespace
      description: Namespace for deployment (defaults to project identifier if empty)
      placeholder: "my-awesome-app"
    validations:
      required: false

  - type: input
    id: replicas
    attributes:
      label: Replica Count
      description: Number of application replicas
      placeholder: "1"
    validations:
      required: true

  - type: input
    id: cpu_request
    attributes:
      label: CPU Request
      description: CPU resource request (e.g., 100m, 0.5, 1)
      placeholder: "100m"
    validations:
      required: true

  - type: input
    id: cpu_limit
    attributes:
      label: CPU Limit
      description: CPU resource limit (e.g., 500m, 1, 2)
      placeholder: "500m"
    validations:
      required: true

  - type: input
    id: memory_request
    attributes:
      label: Memory Request
      description: Memory resource request (e.g., 128Mi, 256Mi, 1Gi)
      placeholder: "256Mi"
    validations:
      required: true

  - type: input
    id: memory_limit
    attributes:
      label: Memory Limit
      description: Memory resource limit (e.g., 512Mi, 1Gi, 2Gi)
      placeholder: "512Mi"
    validations:
      required: true

  - type: input
    id: container_port
    attributes:
      label: Container Port
      description: |
        Port your application listens on:
        - React Frontend: 80 (production) or 3000 (development)
        - Spring Boot Backend: 8080 (default)
        - Other applications: Custom port
      placeholder: "8080"
    validations:
      required: true

  - type: dropdown
    id: service_type
    attributes:
      label: Service Type
      description: Kubernetes service type
      options:
        - ClusterIP
        - NodePort
        - LoadBalancer
      default: 2
    validations:
      required: true

  - type: input
    id: node_port
    attributes:
      label: NodePort (if applicable)
      description: Specific NodePort (30000-32767, leave empty for auto-assignment)
      placeholder: "30080"
    validations:
      required: false

  - type: checkboxes
    id: enable_ingress
    attributes:
      label: Enable Ingress
      description: Create ingress configuration for external access
      options:
        - label: Enable ingress for external access
          required: false
    validations:
      required: false

  - type: input
    id: ingress_host
    attributes:
      label: Ingress Host
      description: Domain name for ingress (required if ingress enabled)
      placeholder: "my-app.example.com"
    validations:
      required: false

  - type: input
    id: ingress_path
    attributes:
      label: Ingress Path
      description: Path for ingress routing
      placeholder: "/"
    validations:
      required: false

  - type: markdown
    attributes:
      value: |
        ## 🔧 Application Configuration

        Configure your application's environment variables and secrets. For NestJS applications, common configurations are provided below.

  - type: input
    id: app_url
    attributes:
      label: Application URL
      description: Base URL for your application (used for CORS and redirects)
      placeholder: "http://localhost:3000"
    validations:
      required: false

  - type: input
    id: api_url
    attributes:
      label: API URL
      description: API base URL (if different from app URL)
      placeholder: "http://localhost:3000"
    validations:
      required: false

  - type: input
    id: cors_origins
    attributes:
      label: CORS Allowed Origins
      description: Comma-separated list of allowed CORS origins
      placeholder: "http://localhost:3000,http://localhost:3001"
    validations:
      required: false

  - type: input
    id: jwt_expiration
    attributes:
      label: JWT Token Expiration
      description: JWT token expiration time in milliseconds
      placeholder: "86400000"
    validations:
      required: false

  - type: markdown
    attributes:
      value: |
        ## 📧 SMTP Configuration (for email functionality)

  - type: input
    id: smtp_host
    attributes:
      label: SMTP Host
      description: SMTP server hostname
      placeholder: "smtp.gmail.com"
    validations:
      required: false

  - type: input
    id: smtp_port
    attributes:
      label: SMTP Port
      description: SMTP server port
      placeholder: "587"
    validations:
      required: false

  - type: input
    id: smtp_from
    attributes:
      label: SMTP From Address
      description: Default sender email address
      placeholder: '"No Reply" <<EMAIL>>'
    validations:
      required: false

  - type: markdown
    attributes:
      value: |
        ## 🔐 OAuth2 Configuration (for Google OAuth)

  - type: input
    id: google_redirect_uri
    attributes:
      label: Google OAuth Redirect URI
      description: OAuth2 callback URL for Google authentication
      placeholder: "http://localhost:8080/oauth2/callback/google"
    validations:
      required: false

  - type: input
    id: oauth_scopes
    attributes:
      label: OAuth2 Scopes
      description: Comma-separated OAuth2 scopes
      placeholder: "email,profile,openid"
    validations:
      required: false

  - type: textarea
    id: oauth_redirect_uris
    attributes:
      label: Authorized Redirect URIs
      description: |
        List of authorized redirect URIs (one per line)
        Include all platforms (web, mobile, etc.)
      placeholder: |
        http://localhost:8080/oauth2/callback/google
        http://localhost:3000/oauth2/redirect
        myandroidapp://oauth2/redirect
        myiosapp://oauth2/redirect
    validations:
      required: false

  - type: textarea
    id: env_vars
    attributes:
      label: Additional Environment Variables
      description: |
        Additional environment variables (one per line, format: KEY=value)
        These will be added to the ConfigMap along with the defaults above
      placeholder: |
        LOG_LEVEL=info
        API_VERSION=v1
        CUSTOM_FEATURE_FLAG=true
    validations:
      required: false

  - type: textarea
    id: secret_keys
    attributes:
      label: Additional Secret Keys
      description: |
        Secret keys in KEY=VALUE format (one per line)
        Include standard secrets with actual values to override placeholders:
        JWT_SECRET, DB_PASSWORD, SMTP_USER, SMTP_PASS, GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET
      placeholder: |
        JWT_SECRET=supersecretkey
        DB_PASSWORD=password
        SMTP_USER=<EMAIL>
        SMTP_PASS=fqactehafmzlltzz
        GOOGLE_CLIENT_ID=1073981864538-3uiik72ohsfr2ouioror3fm1jqc493os.apps.googleusercontent.com
        GOOGLE_CLIENT_SECRET=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
        CUSTOM_API_KEY=your-custom-key
        THIRD_PARTY_SECRET=your-third-party-secret
    validations:
      required: false

  - type: checkboxes
    id: enable_database
    attributes:
      label: Enable PostgreSQL Database
      description: Include PostgreSQL database deployment
      options:
        - label: Include PostgreSQL database deployment
          required: false
    validations:
      required: false

  - type: input
    id: db_name
    attributes:
      label: Database Name
      description: PostgreSQL database name (required if database enabled)
      placeholder: "myapp_db"
    validations:
      required: false

  - type: input
    id: db_user
    attributes:
      label: Database User
      description: PostgreSQL username (defaults to 'postgres' if empty)
      placeholder: "postgres"
    validations:
      required: false

  - type: input
    id: storage_size
    attributes:
      label: Storage Size
      description: Persistent volume size for database (e.g., 1Gi, 5Gi, 10Gi)
      placeholder: "5Gi"
    validations:
      required: false

  - type: checkboxes
    id: enable_pvc
    attributes:
      label: Enable Persistent Volume
      description: Create PVC for application data storage
      options:
        - label: Create PVC for application data storage
          required: false
    validations:
      required: false

  - type: input
    id: pvc_size
    attributes:
      label: PVC Size
      description: Size for application PVC (required if PVC enabled)
      placeholder: "1Gi"
    validations:
      required: false

  - type: input
    id: pvc_mount_path
    attributes:
      label: PVC Mount Path
      description: Container path to mount the PVC
      placeholder: "/app/data"
    validations:
      required: false

  - type: dropdown
    id: app_type
    attributes:
      label: Application Type
      description: |
        Type of application for optimized configuration and defaults:
        - **react-frontend**: React/Vue/Angular apps (port 80/3000, static serving)
        - **springboot-backend**: Spring Boot APIs (port 8080, database integration)
        - **web-app**: General web applications
        - **api**: REST/GraphQL APIs
        - **microservice**: Microservice applications
      options:
        - react-frontend
        - springboot-backend
        - web-app
        - api
        - microservice
        - worker
        - database
      default: 0
    validations:
      required: true

  - type: input
    id: health_check_path
    attributes:
      label: Health Check Path
      description: |
        HTTP path for health checks (leave empty to disable):
        - React Frontend: "/" or "/health"
        - Spring Boot Backend: "/actuator/health"
        - Other applications: Custom health endpoint
      placeholder: "/health"
    validations:
      required: false

  - type: textarea
    id: additional_notes
    attributes:
      label: Additional Notes
      description: Any additional configuration requirements or notes
      placeholder: "Special networking requirements, dependencies, etc."
    validations:
      required: false

  - type: markdown
    attributes:
      value: |
        ---
        
        ## 📋 What happens next?
        
        1. **Validation**: The automation will validate your inputs
        2. **Generation**: Complete project structure will be created
        3. **Commit**: Files will be committed to the repository
        4. **Notification**: You'll receive deployment instructions
        5. **ArgoCD**: Your application will be ready for ArgoCD deployment
        
        **⚠️ Important Notes:**
        - Secret values will be created as placeholders - update them manually
        - Review generated manifests before deploying to production
        - Database passwords and sensitive data need manual configuration
        
        **🔗 Useful Links:**
        - [ArgoCD Documentation](https://argo-cd.readthedocs.io/)
        - [Kubernetes Documentation](https://kubernetes.io/docs/)
        - [Repository README](../README.md)
