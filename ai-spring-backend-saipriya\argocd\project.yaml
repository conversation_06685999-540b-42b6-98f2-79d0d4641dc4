apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: ai-spring-backend-saipriya-project
  namespace: argocd
  labels:
    app.kubernetes.io/name: ai-spring-backend-saipriya-project
    environment: dev
spec:
  description: "ai-spring-backend-saipriya Project for GitOps deployment"
  sourceRepos:
  - 'https://github.com/ChidhagniConsulting/gitops-argocd-apps.git'
  - 'https://github.com/ChidhagniConsulting/gitops-argocd-apps'
  destinations:
  - namespace: ai-spring-backend-saipriya-dev
    server: https://6be4e15d-52f9-431d-84ec-ec8cad0dff2d.k8s.ondigitalocean.com
  - namespace: argocd
    server: https://kubernetes.default.svc
  clusterResourceWhitelist:
  - group: ''
    kind: Namespace
  - group: ''
    kind: PersistentVolume
  - group: storage.k8s.io
    kind: StorageClass
  namespaceResourceWhitelist:
  - group: ''
    kind: ConfigMap
  - group: ''
    kind: Secret
  - group: ''
    kind: Service
  - group: ''
    kind: PersistentVolumeClaim
  - group: apps
    kind: Deployment
  - group: batch
    kind: Job
  - group: networking.k8s.io
    kind: Ingress
  roles:
  - name: admin
    description: "Admin access to ai-spring-backend-saipriya project"
    policies:
    - p, proj:ai-spring-backend-saipriya-project:admin, applications, *, ai-spring-backend-saipriya-project/*, allow
    - p, proj:ai-spring-backend-saipriya-project:admin, repositories, *, *, allow
    groups:
    - argocd:admin
