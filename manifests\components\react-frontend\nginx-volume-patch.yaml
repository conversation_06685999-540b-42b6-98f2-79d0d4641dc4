apiVersion: apps/v1
kind: Deployment
metadata:
  name: placeholder
spec:
  template:
    spec:
      containers:
      - name: app
        volumeMounts:
        - name: nginx-conf
          mountPath: /etc/nginx/conf.d/default.conf
          subPath: default.conf
        - name: env-config
          mountPath: /usr/share/nginx/html/env-config.js
          subPath: env-config.js
      volumes:
      - name: nginx-conf
        configMap:
          name: app-nginx-config
      - name: env-config
        configMap:
          name: app-env-config
