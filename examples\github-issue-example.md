# Example GitHub Issue Configuration

This example shows how to fill out the enhanced GitHub Issue form to create a fully configured NestJS application.

## Sample GitHub Issue Form Values

### Basic Configuration
- **Application Name**: `my-nestjs-app`
- **Project Identifier**: `my-nestjs-app`
- **Container Image**: `docker.io/myorg/my-nestjs-app:v1.0.0`
- **Environment**: `dev`
- **Replica Count**: `1`

### Resource Configuration
- **CPU Request**: `100m`
- **CPU Limit**: `500m`
- **Memory Request**: `256Mi`
- **Memory Limit**: `512Mi`
- **Container Port**: `8080`

### Application Configuration
- **Application URL**: `https://my-app.example.com`
- **API URL**: `https://api.my-app.example.com`
- **CORS Allowed Origins**: `https://my-app.example.com,https://admin.my-app.example.com`
- **JWT Token Expiration**: `86400000`

### SMTP Configuration
- **SMTP Host**: `smtp.gmail.com`
- **SMTP Port**: `587`
- **SMTP From Address**: `"My App" <<EMAIL>>`

### OAuth2 Configuration
- **Google OAuth Redirect URI**: `https://api.my-app.example.com/oauth2/callback/google`
- **OAuth2 Scopes**: `email,profile,openid`
- **Authorized Redirect URIs**:
  ```
  https://api.my-app.example.com/oauth2/callback/google
  https://my-app.example.com/oauth2/redirect
  myapp://oauth2/redirect
  ```

### Database Configuration
- **Enable PostgreSQL Database**: ✅ Checked
- **Database Name**: `myapp_production`
- **Database User**: `myapp_user`

### Additional Configuration
- **Health Check Path**: `/api/v1/health`
- **Additional Environment Variables**:
  ```
  LOG_LEVEL=info
  API_VERSION=v2
  FEATURE_FLAG_NEW_UI=true
  ```
- **Additional Secret Keys**:
  ```
  STRIPE_SECRET_KEY
  SENDGRID_API_KEY
  ```

## Generated ConfigMap

The above configuration would generate a ConfigMap like this:

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: my-nestjs-app-config
  namespace: my-nestjs-app
data:
  NODE_ENV: "dev"
  PORT: "8080"
  APP_URL: "https://my-app.example.com"
  API_URL: "https://api.my-app.example.com"
  DB_HOST: "postgres-service"
  DB_PORT: "5432"
  DB_NAME: "myapp_production"
  JWT_EXPIRATION: "86400000"
  CORS_ALLOWED_ORIGINS: "https://my-app.example.com,https://admin.my-app.example.com"
  CORS_ALLOWED_METHODS: "GET,POST,PUT,DELETE,PATCH,OPTIONS"
  CORS_ALLOW_CREDENTIALS: "true"
  CORS_MAX_AGE: "3600"
  SMTP_HOST: "smtp.gmail.com"
  SMTP_PORT: "587"
  SMTP_FROM: "\"My App\" <<EMAIL>>"
  GOOGLE_REDIRECT_URI: "https://api.my-app.example.com/oauth2/callback/google"
  GOOGLE_SCOPE: "email,profile,openid"
  OAUTH2_AUTHORIZED_REDIRECT_URIS: "https://api.my-app.example.com/oauth2/callback/google,https://my-app.example.com/oauth2/redirect,myapp://oauth2/redirect"
  LOG_LEVEL: "info"
  API_VERSION: "v2"
  FEATURE_FLAG_NEW_UI: "true"
```

## Generated Secret

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: my-nestjs-app-secrets
  namespace: my-nestjs-app
type: Opaque
data:
  JWT_SECRET: UExBQ0VIT0xERVI=  # PLACEHOLDER
  DB_USER: bXlhcHBfdXNlcg==  # myapp_user
  DB_PASSWORD: UExBQ0VIT0xERVI=  # PLACEHOLDER
  SMTP_USER: UExBQ0VIT0xERVI=  # PLACEHOLDER
  SMTP_PASS: UExBQ0VIT0xERVI=  # PLACEHOLDER
  GOOGLE_CLIENT_ID: UExBQ0VIT0xERVI=  # PLACEHOLDER
  GOOGLE_CLIENT_SECRET: UExBQ0VIT0xERVI=  # PLACEHOLDER
  STRIPE_SECRET_KEY: UExBQ0VIT0xERVI=  # PLACEHOLDER
  SENDGRID_API_KEY: UExBQ0VIT0xERVI=  # PLACEHOLDER
```

## Benefits

1. **No Hard-coded Values**: Everything is configurable per application
2. **Production Ready**: Proper URLs, CORS settings, OAuth configuration
3. **Flexible**: Supports different environments and requirements
4. **Secure**: All secrets are placeholders that need manual configuration
5. **Complete**: Includes all necessary configuration for NestJS applications

## Next Steps

After the manifests are generated:
1. Update the secret values with actual base64-encoded credentials
2. Review the generated manifests
3. Deploy with ArgoCD
4. Monitor the application health
