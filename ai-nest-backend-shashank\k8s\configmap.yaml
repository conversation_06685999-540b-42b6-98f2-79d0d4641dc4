apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-nest-backend-shashank-config
  namespace: ai-nest-backend-shashank-dev
  labels:
    app: ai-nest-backend-shashank
    component: config
    environment: dev
    app-type: nest-backend
data:
  # Application Configuration
  NODE_ENV: "dev"
  PORT: "3000"
  # NestJS Backend Configuration
  NODE_ENV: "dev"
  APP_PORT: "3000"
  # NestJS Database Configuration (Managed DigitalOcean Database)
  DATABASE_URL: "postgresql://spring_dev_user:<EMAIL>:25060/spring_dev_db?sslmode=require"
  DB_HOST: "private-dbaas-db-10329328-do-user-23815742-0.m.db.ondigitalocean.com"
  DB_PORT: "25060"
  DB_NAME: "spring_dev_db"
  DB_USERNAME: "spring_dev_user"
  DB_SSL_MODE: "require"
  # NestJS Application URLs
  APP_URL: "http://ai-nest-backend-shashank.dev.local"
  API_URL: "http://ai-nest-backend-shashank-service:3000"
  # NestJS CORS Configuration
  CORS_ORIGIN: "http://localhost:3000,http://localhost:3001"
  CORS_CREDENTIALS: "true"
  # NestJS Security & JWT Configuration
  JWT_EXPIRATION: "24h"
  JWT_ALGORITHM: "HS256"
  # NestJS SMTP Configuration
  SMTP_HOST: "smtp.gmail.com"
  SMTP_PORT: "587"
  SMTP_FROM: "<EMAIL>"
  SMTP_SECURE: "false"
  SMTP_TLS: "true"
  # NestJS OAuth2 Configuration
  GOOGLE_CLIENT_ID: "1073981864538-3uiik72ohsfr2ouioror3fm1jqc493os.apps.googleusercontent.com"
  GOOGLE_REDIRECT_URI: "http://ai-nest-backend-shashank.dev.local/auth/google/callback"
  OAUTH_SCOPES: "openid,profile,email"
