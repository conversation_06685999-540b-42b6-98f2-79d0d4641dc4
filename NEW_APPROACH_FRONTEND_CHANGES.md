# 🚀 New Approach - Frontend Runtime Configuration Changes

## Overview
This document outlines the **NEW APPROACH** for implementing runtime environment configuration in the `ai-react-frontend` project. This approach uses a JavaScript configuration file that can be dynamically updated at runtime without rebuilding the Docker image.

## 🎯 Objective
Enable the React frontend to load backend URLs from a runtime configuration file (`env-config.js`) that is mounted from a Kubernetes ConfigMap, allowing dynamic backend switching without image rebuilds.

---

## 📁 Current Project Analysis

### Current Setup
- **Build Tool**: Vite with TypeScript
- **Current Config**: Uses `runtime-config.json` and `configService.ts`
- **Environment Management**: Multiple `.env` files with structured naming
- **Backend URLs**: 
  - Spring: `http://localhost:8080` (dev), `http://*************:8080` (external)
  - Django: `http://*************:8000`
  - Nest: `http://**************:3000`

---

## 🔄 New Approach Implementation

### 1. Create Runtime Environment Configuration File

**File**: `public/env-config.js`
```javascript
// public/env-config.js
window._env_ = {
  REACT_APP_BACKEND_URL: "http://localhost:8080", // default value (overwritten in K8s)
  REACT_APP_CURRENT_BACKEND: "spring",
  REACT_APP_ENVIRONMENT: "development",
  REACT_APP_API_VERSION: "v1"
};
```

### 2. Update HTML to Load Runtime Config

**File**: `index.html` (Add script tag)
```html
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AI React Frontend - Modern Web Application</title>
    <!-- ... existing meta tags ... -->
    
    <!-- Runtime Environment Configuration -->
    <script src="%PUBLIC_URL%/env-config.js"></script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
```

### 3. Update Configuration Service

**File**: `src/services/configService.ts` (Update to use window._env_)
```typescript
interface RuntimeConfig {
  currentBackend: 'spring' | 'nest' | 'django';
  backendUrl: string;
  environment: string;
  serviceName?: string;
  namespace?: string;
  apiVersion?: string;
}

class ConfigService {
  private config: RuntimeConfig | null = null;
  private readonly FALLBACK_URLS = {
    spring: 'http://localhost:8080',
    nest: 'http://localhost:3000', 
    django: 'http://localhost:8000'
  };

  async loadConfig(): Promise<RuntimeConfig> {
    // First try to load from window._env_ (runtime config)
    if (typeof window !== 'undefined' && window._env_) {
      this.config = {
        currentBackend: window._env_.REACT_APP_CURRENT_BACKEND || 'spring',
        backendUrl: window._env_.REACT_APP_BACKEND_URL || this.FALLBACK_URLS.spring,
        environment: window._env_.REACT_APP_ENVIRONMENT || 'development',
        apiVersion: window._env_.REACT_APP_API_VERSION || 'v1'
      };
      return this.config;
    }

    // Fallback to existing runtime-config.json approach
    try {
      const response = await fetch('/api/config');
      if (response.ok) {
        this.config = await response.json();
        return this.config;
      }
    } catch (error) {
      console.warn('Failed to load runtime config, using fallback');
    }

    return this.getFallbackConfig();
  }

  private getFallbackConfig(): RuntimeConfig {
    const backendType = this.getBackendFromEnv();
    
    return {
      currentBackend: backendType,
      backendUrl: import.meta.env.VITE_APP_API_URL || this.FALLBACK_URLS[backendType],
      environment: import.meta.env.VITE_APP_ENV || 'dev',
      serviceName: import.meta.env.VITE_APP_SERVICE_NAME,
      namespace: import.meta.env.VITE_APP_BACKEND_NAMESPACE,
      apiVersion: import.meta.env.VITE_APP_API_VERSION || 'v1'
    };
  }

  // ... rest of the existing methods
}

// Global type declaration for window._env_
declare global {
  interface Window {
    _env_: {
      REACT_APP_BACKEND_URL: string;
      REACT_APP_CURRENT_BACKEND: string;
      REACT_APP_ENVIRONMENT: string;
      REACT_APP_API_VERSION: string;
    };
  }
}

export const configService = new ConfigService();

// Updated API base URL getter
export const getApiBaseUrl = async (): Promise<string> => {
  try {
    // Use window._env_ if available (runtime config)
    if (typeof window !== 'undefined' && window._env_?.REACT_APP_BACKEND_URL) {
      return window._env_.REACT_APP_BACKEND_URL;
    }
    
    // Fallback to service
    return await configService.getCurrentBackendUrl();
  } catch {
    return import.meta.env.VITE_APP_API_URL || 'http://localhost:8080';
  }
};
```

### 4. Update Environment Configuration

**File**: `src/config/env.ts` (Add runtime config support)
```typescript
// Environment configuration helper
import { getApiBaseUrl as getRuntimeApiBaseUrl } from '../services/configService';

// New runtime API base URL getter (recommended)
export const getApiBaseUrl = (): string => {
  // For testing environment
  if (typeof process !== 'undefined' && process.env.NODE_ENV === 'test') {
    return process.env.VITE_APP_API_URL || 'http://localhost:8080';
  }

  // For runtime environment - check window._env_ first
  if (typeof window !== 'undefined' && window._env_?.REACT_APP_BACKEND_URL) {
    return window._env_.REACT_APP_BACKEND_URL;
  }

  // Fallback to build-time environment
  try {
    return import.meta.env.VITE_APP_API_URL || 'http://localhost:8080';
  } catch {
    return 'http://localhost:8080';
  }
};

// Async version for new code
export const getApiBaseUrlAsync = async (): Promise<string> => {
  try {
    return await getRuntimeApiBaseUrl();
  } catch {
    return getApiBaseUrl();
  }
};

// Runtime environment detection
export const getCurrentEnvironment = () => {
  if (typeof window !== 'undefined' && window._env_?.REACT_APP_ENVIRONMENT) {
    return window._env_.REACT_APP_ENVIRONMENT;
  }
  return import.meta.env.VITE_APP_ENV || 'dev';
};

// Runtime backend detection
export const getCurrentBackend = () => {
  if (typeof window !== 'undefined' && window._env_?.REACT_APP_CURRENT_BACKEND) {
    return window._env_.REACT_APP_CURRENT_BACKEND;
  }
  const serviceName = import.meta.env.VITE_APP_SERVICE_NAME || '';
  if (serviceName.includes('spring')) return 'spring';
  if (serviceName.includes('nest')) return 'nest';
  if (serviceName.includes('django')) return 'django';
  return 'spring';
};

// ... rest of existing configuration functions
```

---

## 🔧 Benefits of New Approach

1. **✅ Runtime Configuration**: No Docker image rebuild required
2. **✅ Simple Implementation**: Uses standard JavaScript file approach
3. **✅ Browser Compatibility**: Works in all browsers without fetch API issues
4. **✅ Immediate Loading**: Configuration available before React app starts
5. **✅ Fallback Support**: Graceful degradation to existing config methods
6. **✅ Easy Debugging**: Configuration visible in browser dev tools

---

## 📋 Implementation Checklist

### Frontend Changes
- [ ] Create `public/env-config.js` with default configuration
- [ ] Update `index.html` to include env-config.js script
- [ ] Update `src/services/configService.ts` to use window._env_
- [ ] Update `src/config/env.ts` with runtime config support
- [ ] Add TypeScript declarations for window._env_
- [ ] Test local development with new configuration

### Testing
- [ ] Verify configuration loads correctly in browser
- [ ] Test fallback behavior when env-config.js is not available
- [ ] Ensure existing tests continue to pass
- [ ] Test with different backend URLs

### Documentation
- [ ] Update README with new configuration approach
- [ ] Document how to switch backends using new method
- [ ] Create troubleshooting guide for configuration issues

---

## 🚨 Important Notes

1. **Backward Compatibility**: This approach maintains compatibility with existing `runtime-config.json` method
2. **Testing Environment**: Tests will continue to use environment variables as before
3. **Build Process**: No changes required to existing build scripts
4. **Environment Files**: Existing `.env.*` files remain functional for build-time configuration
5. **Production Ready**: This approach is production-ready and widely used in React applications

---

## 🔄 Migration Path

1. **Phase 1**: Implement new env-config.js approach alongside existing system
2. **Phase 2**: Test thoroughly in development environment
3. **Phase 3**: Deploy to staging with ConfigMap integration
4. **Phase 4**: Gradually migrate all environments to new approach
5. **Phase 5**: Remove legacy runtime-config.json system (optional)

This new approach provides a cleaner, more standard way to handle runtime configuration in React applications while maintaining full backward compatibility.
