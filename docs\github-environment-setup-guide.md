# GitHub Environment Setup Guide for GitOps Approval Workflow

This guide explains how to set up GitHub Environment Protection Rules to enable proper approval notifications for your GitOps promotion workflow.

## Problem Summary

Your GitOps promotion workflow is configured to use GitHub Environments (`staging-approval` and `production-approval`) for manual approvals, but the environments may not be properly configured with protection rules, which is why @AshrafSyed25 is not receiving approval notifications.

## Solution: Configure GitHub Environment Protection Rules

### Step 1: Create GitHub Environments

1. **Navigate to Repository Settings**:
   - Go to your repository on GitHub
   - Click on **Settings** tab
   - In the left sidebar, click on **Environments**

2. **Create Staging Approval Environment**:
   - Click **New environment**
   - Name: `staging-approval`
   - Click **Configure environment**

3. **Create Production Approval Environment**:
   - Click **New environment**
   - Name: `production-approval`
   - Click **Configure environment**

### Step 2: Configure Protection Rules for Staging Environment

For the `staging-approval` environment:

1. **Required Reviewers**:
   - ✅ Check "Required reviewers"
   - Add `AshrafSyed25` as a required reviewer
   - Set "Number of required reviewers" to 1

2. **Wait Timer** (Optional):
   - ⚪ Leave unchecked or set to 0 minutes for immediate approval

3. **Deployment Branches**:
   - Select "Protected branches only" or "Selected branches"
   - Add your main branch (usually `main` or `master`)

4. **Prevent Administrators from Bypassing**:
   - ✅ Check this option for security (recommended)

### Step 3: Configure Protection Rules for Production Environment

For the `production-approval` environment:

1. **Required Reviewers**:
   - ✅ Check "Required reviewers"
   - Add `AshrafSyed25` as a required reviewer
   - Set "Number of required reviewers" to 1

2. **Wait Timer** (Optional):
   - ⚪ Leave unchecked or set to 0 minutes for immediate approval

3. **Deployment Branches**:
   - Select "Protected branches only" or "Selected branches"
   - Add your main branch (usually `main` or `master`)

4. **Prevent Administrators from Bypassing**:
   - ✅ Check this option for security (recommended)

### Step 4: Verify Notification Settings

Ensure @AshrafSyed25 has proper notification settings:

1. **GitHub Notification Settings**:
   - Go to GitHub → Settings → Notifications
   - Under "Actions", ensure notifications are enabled
   - Check both email and web notifications

2. **Repository Watch Settings**:
   - @AshrafSyed25 should watch the repository
   - Go to repository → Click "Watch" → Select "Custom"
   - Enable "Actions" notifications

### Step 5: Test the Approval Process

1. **Trigger a Dev Deployment**:
   - Deploy to dev environment using your CI/CD pipeline
   - This should automatically trigger the staging promotion workflow

2. **Check for Approval Request**:
   - Go to GitHub Actions → Find the running workflow
   - Look for the "promote-to-staging" job
   - It should show "Waiting for approval"

3. **Approve the Deployment**:
   - Click "Review deployments"
   - Select "staging-approval" environment
   - Add optional comment
   - Click "Approve and deploy"

## How the Approval Process Works

### Workflow Behavior

1. **Dev Deployment Completes**: The initial deployment to dev environment finishes successfully
2. **Staging Promotion Triggered**: The `promote-to-staging` job starts automatically
3. **Approval Required**: The job waits for manual approval due to `environment: staging-approval`
4. **Notification Sent**: GitHub sends notification to required reviewers (@AshrafSyed25)
5. **Manual Approval**: Reviewer approves the deployment through GitHub UI
6. **Staging Deployment**: After approval, the staging deployment proceeds

### Notification Channels

GitHub will notify @AshrafSyed25 through:
- **Email notifications** (if enabled in settings)
- **Web notifications** on GitHub
- **Mobile notifications** (if GitHub mobile app is installed)

## Troubleshooting

### Issue: No Notifications Received

**Check:**
1. Environment protection rules are properly configured
2. @AshrafSyed25 is added as a required reviewer
3. Notification settings are enabled in GitHub
4. Repository is being watched with Actions notifications enabled

### Issue: Approval Button Not Visible

**Check:**
1. User has appropriate permissions on the repository
2. Environment protection rules include the correct branch
3. Workflow is running on the correct branch

### Issue: Workflow Stuck in Approval

**Check:**
1. Environment name in workflow matches created environment exactly
2. Required reviewers are available and have access
3. No conflicting branch protection rules

## Verification Commands

You can verify your setup using the GitHub CLI:

```bash
# List environments
gh api repos/:owner/:repo/environments

# Check environment protection rules
gh api repos/:owner/:repo/environments/staging-approval

# Check environment protection rules
gh api repos/:owner/:repo/environments/production-approval
```

## Next Steps

After completing this setup:

1. **Test the workflow** with a dev deployment
2. **Verify notifications** are received by @AshrafSyed25
3. **Document the approval process** for your team
4. **Consider adding additional reviewers** for redundancy

## Related Files

- **Workflow**: `.github/workflows/deploy-from-cicd.yaml`
- **CODEOWNERS**: `.github/CODEOWNERS` (for file-based approvals)
- **Documentation**: `docs/gitops-promotion-workflow.md`
