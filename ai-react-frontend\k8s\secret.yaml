apiVersion: v1
kind: Secret
metadata:
  name: ai-react-frontend-secrets
  namespace: ai-react-frontend-dev
  labels:
    app: ai-react-frontend
    component: secrets
    environment: dev
    app-type: react-frontend
type: Opaque
data:
  # React Frontend - Minimal secrets (typically API keys for build-time)
  # Most secrets are handled by the backend API
  # Add custom secrets here if needed for your React app
  # Custom Secret Keys (add manually if needed)
  # CUSTOM_SECRET_KEY: UExBQ0VIT0xERVI=  # PLACEHOLDER - Update with actual base64 encoded value
