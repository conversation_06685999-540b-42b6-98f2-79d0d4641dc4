apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: ai-react-frontend
  namespace: argocd
  labels:
    app.kubernetes.io/name: ai-react-frontend
    app.kubernetes.io/part-of: ai-react-frontend
    environment: dev
    app-type: react-frontend
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: ai-react-frontend-project
  source:
    repoURL: https://github.com/ChidhagniConsulting/gitops-argocd-apps
    targetRevision: main
    path: ai-react-frontend/k8s
  destination:
    server: https://6be4e15d-52f9-431d-84ec-ec8cad0dff2d.k8s.ondigitalocean.com
    namespace: ai-react-frontend-dev
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
    syncOptions:
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  revisionHistoryLimit: 10
  ignoreDifferences:
  - group: apps
    kind: Deployment
    jsonPointers:
    - /spec/replicas
  info:
  - name: Description
    value: "AI React Frontend - React Frontend Application"
  - name: Repository
    value: "https://github.com/ChidhagniConsulting/gitops-argocd-apps"
  - name: Environment
    value: "dev"
  - name: Application Type
    value: "react-frontend"
  - name: Configuration
    value: "Static serving, build-time env vars"
