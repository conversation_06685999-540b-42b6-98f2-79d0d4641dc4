# Backend switching aliases for Bash/WSL
# Add these to your ~/.bashrc or ~/.zshrc

# Get the script directory (adjust path as needed)
SCRIPT_DIR="$HOME/gitops-argocd-apps/k8s-scripts"

# Backend switching aliases
alias switch-spring="$SCRIPT_DIR/switch-backend.sh spring"
alias switch-django="$SCRIPT_DIR/switch-backend.sh django"
alias switch-nest="$SCRIPT_DIR/switch-backend.sh nest"

# Status and monitoring aliases
alias current-backend="kubectl get configmap ai-react-frontend-runtime-config -n ai-react-frontend-dev -o jsonpath='{.data.runtime-config\.json}' | jq .currentBackend"
alias backend-status="kubectl get configmap ai-react-frontend-runtime-config -n ai-react-frontend-dev -o jsonpath='{.data.runtime-config\.json}' | jq ."
alias health-check="$SCRIPT_DIR/backend-health.sh"
alias frontend-logs="kubectl logs -f deployment/ai-react-frontend -n ai-react-frontend-dev"

# Quick access aliases
alias frontend-pods="kubectl get pods -n ai-react-frontend-dev -l app=ai-react-frontend"
alias frontend-config="curl -s http://*************:3000/api/config | jq ."

echo "✅ Backend switching aliases loaded!"
echo "Available commands:"
echo "  switch-spring    - Switch to Spring Boot backend"
echo "  switch-django    - Switch to Django backend"
echo "  switch-nest      - Switch to NestJS backend"
echo "  current-backend  - Show current backend"
echo "  backend-status   - Show full backend configuration"
echo "  health-check     - Check health of all backends"
echo "  frontend-logs    - Monitor frontend logs"
echo "  frontend-pods    - Show frontend pods"
echo "  frontend-config  - Test frontend config API"
