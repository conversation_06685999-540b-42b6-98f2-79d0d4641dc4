# Application CI/CD Integration Guide

This guide explains how to integrate your application's CI/CD pipeline with the GitOps ArgoCD deployment automation.

## Overview

The integration allows your application repository to automatically trigger ArgoCD deployments when code is merged to the main branch, using the `repository_dispatch` GitHub API.

## Integration Steps

### 1. Add GitOps Deployment Step to Your Application Workflow

Add the following step to your application's CI/CD workflow **after** the Docker image build and push steps:

```yaml
      - name: 🚀 Trigger GitOps Deployment
        if: github.ref == 'refs/heads/main' && github.event_name == 'push'
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITOPS_DEPLOY_TOKEN }}
          script: |
            // Check if this is a merge commit (has multiple parents)
            const commit = await github.rest.git.getCommit({
              owner: context.repo.owner,
              repo: context.repo.repo,
              commit_sha: context.sha
            });
            
            const isMergeCommit = commit.data.parents.length > 1;
            
            if (!isMergeCommit) {
              console.log('Not a merge commit, skipping GitOps deployment');
              return;
            }
            
            console.log('Merge commit detected, triggering GitOps deployment...');
            
            // Extract application details
            const appName = 'Auth App';  // Replace with your app name
            const projectId = 'auth-app';  // Replace with your project ID (lowercase, hyphens only)
            const dockerImage = 'srivani8900/auth-app';  // Replace with your Docker image
            const dockerTag = '${{ steps.docker-build.outputs.image-tag }}';  // Use your image tag output
            
            // Trigger GitOps deployment
            await github.rest.repos.createDispatchEvent({
              owner: 'ChidhagniConsulting',
              repo: 'gitops-argocd-apps',
              event_type: 'deploy-to-argocd',
              client_payload: {
                app_name: appName,
                project_id: projectId,
                environment: 'dev',  // Deploy to dev environment on main branch merges
                docker_image: dockerImage,
                docker_tag: dockerTag,
                source_repo: `${context.repo.owner}/${context.repo.repo}`,
                source_branch: 'main',
                commit_sha: context.sha
              }
            });
            
            console.log(`GitOps deployment triggered for ${appName} (${projectId})`);
            console.log(`Docker image: ${dockerImage}:${dockerTag}`);
```

### 2. Create GitHub Personal Access Token

1. Go to GitHub Settings → Developer settings → Personal access tokens → Tokens (classic)
2. Click "Generate new token (classic)"
3. Set expiration and select these scopes:
   - `repo` (Full control of private repositories)
   - `workflow` (Update GitHub Action workflows)
4. Copy the generated token

### 3. Add Token to Application Repository Secrets

1. Go to your application repository → Settings → Secrets and variables → Actions
2. Click "New repository secret"
3. Name: `GITOPS_DEPLOY_TOKEN`
4. Value: The personal access token from step 2
5. Click "Add secret"

### 4. Customize the Integration

Update the following values in the workflow step:

```yaml
# Application Configuration
const appName = 'Your App Name';           # Human-readable application name
const projectId = 'your-app-id';           # Lowercase alphanumeric with hyphens
const dockerImage = 'your-registry/your-app';  # Your Docker image repository
const dockerTag = '${{ steps.your-build-step.outputs.tag }}';  # Your image tag output

# Environment Configuration
environment: 'dev',  # Target environment (dev, staging, production)
```

### 5. Example Integration for Java Spring Boot App

Here's a complete example for a Java Spring Boot application:

```yaml
      # ... your existing build steps ...
      
      - name: 🐳 Build and Push Docker Image
        id: docker-build
        run: |
          # Build Docker image
          docker build -t srivani8900/auth-app:latest .
          docker build -t srivani8900/auth-app:${{ github.sha }} .
          
          # Push images
          docker push srivani8900/auth-app:latest
          docker push srivani8900/auth-app:${{ github.sha }}
          
          # Output image tag for GitOps
          echo "image-tag=latest" >> $GITHUB_OUTPUT

      - name: 🚀 Trigger GitOps Deployment
        if: github.ref == 'refs/heads/main' && github.event_name == 'push'
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITOPS_DEPLOY_TOKEN }}
          script: |
            const commit = await github.rest.git.getCommit({
              owner: context.repo.owner,
              repo: context.repo.repo,
              commit_sha: context.sha
            });
            
            const isMergeCommit = commit.data.parents.length > 1;
            
            if (!isMergeCommit) {
              console.log('Not a merge commit, skipping GitOps deployment');
              return;
            }
            
            console.log('Merge commit detected, triggering GitOps deployment...');
            
            await github.rest.repos.createDispatchEvent({
              owner: 'ChidhagniConsulting',
              repo: 'gitops-argocd-apps',
              event_type: 'deploy-to-argocd',
              client_payload: {
                app_name: 'Auth App',
                project_id: 'auth-app',
                environment: 'dev',
                docker_image: 'srivani8900/auth-app',
                docker_tag: '${{ steps.docker-build.outputs.image-tag }}',
                source_repo: `${context.repo.owner}/${context.repo.repo}`,
                source_branch: 'main',
                commit_sha: context.sha
              }
            });
            
            console.log('GitOps deployment triggered successfully!');
```

## Environment-Specific Deployments

### Development Environment
- **Trigger**: Merge to `main` branch
- **Environment**: `dev`
- **Replicas**: 1
- **Resources**: Minimal (256Mi memory, 100m CPU)

### Staging Environment
- **Trigger**: Create release tag (e.g., `v1.0.0`)
- **Environment**: `staging`
- **Replicas**: 2
- **Resources**: Medium (512Mi memory, 200m CPU)

### Production Environment
- **Trigger**: Manual approval or specific tag pattern
- **Environment**: `production`
- **Replicas**: 3+
- **Resources**: High (1Gi+ memory, 500m+ CPU)

## Workflow Execution Flow

1. **Developer merges feature branch** → `main` branch
2. **Application CI/CD triggers** → Builds and pushes Docker image
3. **GitOps dispatch sent** → Triggers `deploy-from-cicd.yaml` workflow
4. **Manifests generated** → Using Docker image tag from CI/CD
5. **ArgoCD deployment** → Automatically applies manifests to cluster
6. **Application deployed** → Available in target environment

## Troubleshooting

### Common Issues

1. **Token Permission Error**
   - Ensure `GITOPS_DEPLOY_TOKEN` has `repo` and `workflow` scopes
   - Verify token is not expired

2. **Dispatch Not Triggering**
   - Check if merge commit detection is working
   - Verify repository and event type names are correct

3. **Manifest Generation Fails**
   - Check Docker image and tag format
   - Ensure project ID follows naming conventions

4. **ArgoCD Deployment Fails**
   - Verify `ENABLE_AUTO_DEPLOY` repository variable is set to `true`
   - Check ArgoCD cluster connectivity

### Debug Steps

1. Check GitHub Actions logs in both repositories
2. Verify dispatch payload in GitOps repository workflow logs
3. Check generated manifest files for syntax errors
4. Monitor ArgoCD dashboard for application status

## Security Considerations

- Use repository secrets for sensitive tokens
- Limit token permissions to minimum required scopes
- Consider using GitHub App authentication for enhanced security
- Regularly rotate access tokens

## Next Steps

After integration:
1. Test the workflow with a feature branch merge
2. Monitor deployment in ArgoCD dashboard
3. Verify application health in Kubernetes cluster
4. Set up monitoring and alerting for deployments
