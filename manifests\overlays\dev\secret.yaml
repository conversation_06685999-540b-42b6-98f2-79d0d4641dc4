apiVersion: v1
kind: Secret
metadata:
  name: PLA<PERSON>H<PERSON><PERSON>R_PROJECT_ID-secrets
  labels:
    app: PLACE<PERSON><PERSON>DER_PROJECT_ID
    app.kubernetes.io/name: PLACEHOLDER_PROJECT_ID
    app.kubernetes.io/component: secrets
    app.kubernetes.io/part-of: PLACEHOLDER_APP_NAME
    app.kubernetes.io/version: PLACEHOLDER_COMMIT_SHA
    app.kubernetes.io/managed-by: argocd
    environment: dev
type: Opaque
data:
  # Development Environment Secrets
  # These placeholders will be replaced with base64-encoded values from secrets_encoded

  {{#eq APPLICATION_TYPE "springboot-backend"}}
  # Spring Boot specific secrets
  SPRING_DATASOURCE_URL: DYNAMIC_SPRING_DATASOURCE_URL_B64
  {{/eq}}

  {{#eq APPLICATION_TYPE "django-backend"}}
  DATABASE_URL: DYNAMIC_DATABASE_URL_B64
  {{/eq}}

  {{#eq APPLICATION_TYPE "nest-backend"}}
  DATABASE_URL: DYNAMIC_DATABASE_URL_B64
  {{/eq}}

  # Essential Authentication Secrets
  JWT_SECRET: DYNAMIC_JWT_SECRET_B64
  {{#eq APPLICATION_TYPE "django-backend"}}
  JWT_EXPIRES_IN: DYNAMIC_JWT_EXPIRES_IN_B64
  JWT_REFRESH_EXPIRES_IN: DYNAMIC_JWT_REFRESH_EXPIRES_IN_B64
  {{/eq}}

  # Database Credentials (Development)
  DB_USER: DYNAMIC_DB_USER_B64
  DB_PASSWORD: DYNAMIC_DB_PASSWORD_B64
  DB_HOST: DYNAMIC_DB_HOST_B64
  DB_PORT: DYNAMIC_DB_PORT_B64
  DB_NAME: DYNAMIC_DB_NAME_B64
  DB_SSL_MODE: DYNAMIC_DB_SSL_MODE_B64

  # SMTP Configuration (Development)
  SMTP_USER: DYNAMIC_SMTP_USER_B64
  SMTP_PASS: DYNAMIC_SMTP_PASS_B64

  # OAuth2 Configuration (Development)
  GOOGLE_CLIENT_ID: DYNAMIC_GOOGLE_CLIENT_ID_B64
  GOOGLE_CLIENT_SECRET: DYNAMIC_GOOGLE_CLIENT_SECRET_B64

  {{#eq APPLICATION_TYPE "django-backend"}}
  # Django-specific secrets
  SESSION_SECRET: DYNAMIC_SESSION_SECRET_B64
  RATE_LIMIT_WINDOW_MS: DYNAMIC_RATE_LIMIT_WINDOW_MS_B64
  RATE_LIMIT_MAX_REQUESTS: DYNAMIC_RATE_LIMIT_MAX_REQUESTS_B64
  PASSWORD_RESET_TOKEN_EXPIRY: DYNAMIC_PASSWORD_RESET_TOKEN_EXPIRY_B64
  EMAIL_VERIFICATION_TOKEN_EXPIRY: DYNAMIC_EMAIL_VERIFICATION_TOKEN_EXPIRY_B64
  {{/eq}}

  # Development-specific secrets
  # DEBUG_MODE: DYNAMIC_DEBUG_MODE_B64
  # LOG_LEVEL: DYNAMIC_LOG_LEVEL_B64
