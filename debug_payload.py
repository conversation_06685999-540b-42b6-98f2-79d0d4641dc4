#!/usr/bin/env python3
"""
Debug script to test payload processing and identify issues
"""

import json
import sys
import os
from pathlib import Path

# Add scripts directory to path
sys.path.append(str(Path(__file__).parent / 'scripts'))

from process_payload import parse_payload, parse_secrets, create_placeholder_mapping

def debug_payload():
    """Debug the payload processing"""
    
    # Sample payload that might be causing issues
    test_payload = {
        "project_id": "ai-spring-backend",
        "application_type": "springboot-backend",
        "environment": "dev",
        "docker_image": "registry.digitalocean.com/doks-registry/ai-spring-backend",
        "docker_tag": "latest",
        "source_repo": "ChidhagniConsulting/ai-spring-backend",
        "source_branch": "main",
        "commit_sha": "f43ad48f9d04ce7c0f6b9e4b8a8d12"
    }
    
    print("🔍 Testing payload processing...")
    print(f"📦 Test payload: {json.dumps(test_payload, indent=2)}")
    
    # Test payload parsing
    parsed_payload = parse_payload(json.dumps(test_payload))
    if not parsed_payload:
        print("❌ Failed to parse payload")
        return False
    
    print("✅ Payload parsed successfully")
    
    # Test secrets parsing (empty for now)
    secrets = parse_secrets("")
    print(f"🔐 Parsed secrets: {len(secrets)} items")
    
    # Test placeholder mapping creation
    mapping = create_placeholder_mapping(parsed_payload, secrets)
    print(f"🗺️ Created mapping with {len(mapping)} placeholders:")
    
    for key, value in mapping.items():
        print(f"  {key}: {value}")
    
    # Check for potential issues
    issues = []
    
    # Check if docker image looks valid
    docker_image = mapping.get('PLACEHOLDER_DOCKER_IMAGE', '')
    if not docker_image or docker_image == 'nginx:latest':
        issues.append(f"Docker image might be invalid: {docker_image}")
    
    # Check if project_id looks valid
    project_id = mapping.get('PLACEHOLDER_PROJECT_ID', '')
    if not project_id or project_id == 'unknown-project':
        issues.append(f"Project ID might be invalid: {project_id}")
    
    # Check for empty values
    for key, value in mapping.items():
        if not value or value == '':
            issues.append(f"Empty value for {key}")
    
    if issues:
        print("\n⚠️ Potential issues found:")
        for issue in issues:
            print(f"  - {issue}")
    else:
        print("\n✅ No obvious issues found in mapping")
    
    return True

if __name__ == "__main__":
    debug_payload()
