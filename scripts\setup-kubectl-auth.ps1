#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Setup kubectl authentication for GitOps automation
    
.DESCRIPTION
    This script helps configure kubectl authentication for the automated ArgoCD deployment system.
    It can create service accounts, generate tokens, and configure kubectl contexts.
    
.PARAMETER Mode
    Setup mode: 'service-account', 'kubeconfig', or 'validate'
    
.PARAMETER ServiceAccountName
    Name of the service account to create (default: github-actions)
    
.PARAMETER Namespace
    Namespace for the service account (default: argocd)
    
.PARAMETER ClusterName
    Name of the Kubernetes cluster
    
.PARAMETER ServerUrl
    Kubernetes API server URL
    
.EXAMPLE
    ./setup-kubectl-auth.ps1 -Mode service-account
    
.EXAMPLE
    ./setup-kubectl-auth.ps1 -Mode validate
#>

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("service-account", "kubeconfig", "validate")]
    [string]$Mode,
    
    [Parameter(Mandatory=$false)]
    [string]$ServiceAccountName = "github-actions",
    
    [Parameter(Mandatory=$false)]
    [string]$Namespace = "argocd",
    
    [Parameter(Mandatory=$false)]
    [string]$ClusterName,
    
    [Parameter(Mandatory=$false)]
    [string]$ServerUrl
)

# Function to check if kubectl is available
function Test-KubectlAvailability {
    try {
        $null = kubectl version --client --short 2>$null
        return $true
    } catch {
        return $false
    }
}

# Function to create service account and RBAC
function New-ServiceAccountSetup {
    param(
        [string]$Name,
        [string]$Namespace
    )
    
    Write-Host "🔧 Creating service account setup for GitOps automation..." -ForegroundColor Green
    
    # Create namespace if it doesn't exist
    Write-Host "📁 Ensuring namespace exists: $Namespace" -ForegroundColor Yellow
    kubectl create namespace $Namespace --dry-run=client -o yaml | kubectl apply -f -
    
    # Create service account
    Write-Host "👤 Creating service account: $Name" -ForegroundColor Yellow
    $serviceAccountYaml = @"
apiVersion: v1
kind: ServiceAccount
metadata:
  name: $Name
  namespace: $Namespace
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: $Name-argocd
rules:
- apiGroups: ["argoproj.io"]
  resources: ["applications", "appprojects"]
  verbs: ["get", "list", "create", "update", "patch", "delete"]
- apiGroups: [""]
  resources: ["namespaces"]
  verbs: ["get", "list", "create"]
- apiGroups: ["apps"]
  resources: ["deployments"]
  verbs: ["get", "list"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: $Name-argocd
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: $Name-argocd
subjects:
- kind: ServiceAccount
  name: $Name
  namespace: $Namespace
"@
    
    $serviceAccountYaml | kubectl apply -f -
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Service account and RBAC created successfully" -ForegroundColor Green
    } else {
        Write-Host "❌ Failed to create service account and RBAC" -ForegroundColor Red
        return $false
    }
    
    # Wait for service account to be ready
    Write-Host "⏳ Waiting for service account to be ready..." -ForegroundColor Yellow
    Start-Sleep -Seconds 5
    
    # Get service account token (for Kubernetes 1.24+)
    Write-Host "🔑 Creating service account token..." -ForegroundColor Yellow
    $tokenYaml = @"
apiVersion: v1
kind: Secret
metadata:
  name: $Name-token
  namespace: $Namespace
  annotations:
    kubernetes.io/service-account.name: $Name
type: kubernetes.io/service-account-token
"@
    
    $tokenYaml | kubectl apply -f -
    
    # Wait for token to be populated
    Start-Sleep -Seconds 5
    
    # Get the token
    $token = kubectl get secret "$Name-token" -n $Namespace -o jsonpath='{.data.token}' | ForEach-Object { [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($_)) }
    
    if ($token) {
        Write-Host "✅ Service account token generated successfully" -ForegroundColor Green
        Write-Host "`n🔑 Service Account Token:" -ForegroundColor Cyan
        Write-Host $token -ForegroundColor White
        
        # Get cluster info
        $serverUrl = kubectl config view --minify -o jsonpath='{.clusters[0].cluster.server}'
        $clusterName = kubectl config view --minify -o jsonpath='{.clusters[0].name}'
        
        Write-Host "`n📋 Configuration Summary:" -ForegroundColor Cyan
        Write-Host "Service Account: $Name" -ForegroundColor White
        Write-Host "Namespace: $Namespace" -ForegroundColor White
        Write-Host "Cluster: $clusterName" -ForegroundColor White
        Write-Host "Server: $serverUrl" -ForegroundColor White
        
        Write-Host "`n📝 GitHub Secrets Configuration:" -ForegroundColor Cyan
        Write-Host "Add these secrets to your GitHub repository:" -ForegroundColor Yellow
        Write-Host "KUBE_SERVER: $serverUrl" -ForegroundColor White
        Write-Host "KUBE_TOKEN: $token" -ForegroundColor White
        
        return $true
    } else {
        Write-Host "❌ Failed to get service account token" -ForegroundColor Red
        return $false
    }
}

# Function to generate kubeconfig
function New-KubeconfigSetup {
    Write-Host "📄 Generating kubeconfig for GitOps automation..." -ForegroundColor Green
    
    if (-not $ClusterName -or -not $ServerUrl) {
        # Try to get from current context
        $ClusterName = kubectl config view --minify -o jsonpath='{.clusters[0].name}'
        $ServerUrl = kubectl config view --minify -o jsonpath='{.clusters[0].cluster.server}'
        
        if (-not $ClusterName -or -not $ServerUrl) {
            Write-Host "❌ ClusterName and ServerUrl are required for kubeconfig generation" -ForegroundColor Red
            return $false
        }
    }
    
    # Get current kubeconfig
    $kubeconfigPath = "$env:HOME/.kube/config"
    if (Test-Path $kubeconfigPath) {
        $kubeconfigContent = Get-Content $kubeconfigPath -Raw
        $kubeconfigBase64 = [System.Convert]::ToBase64String([System.Text.Encoding]::UTF8.GetBytes($kubeconfigContent))
        
        Write-Host "✅ Kubeconfig encoded successfully" -ForegroundColor Green
        Write-Host "`n📝 GitHub Secrets Configuration:" -ForegroundColor Cyan
        Write-Host "Add this secret to your GitHub repository:" -ForegroundColor Yellow
        Write-Host "KUBE_CONFIG: $kubeconfigBase64" -ForegroundColor White
        
        return $true
    } else {
        Write-Host "❌ Kubeconfig not found at $kubeconfigPath" -ForegroundColor Red
        return $false
    }
}

# Function to validate kubectl setup
function Test-KubectlSetup {
    Write-Host "🔍 Validating kubectl setup for GitOps automation..." -ForegroundColor Green
    
    $isValid = $true
    
    # Check kubectl availability
    if (Test-KubectlAvailability) {
        Write-Host "✅ kubectl is available" -ForegroundColor Green
    } else {
        Write-Host "❌ kubectl is not available" -ForegroundColor Red
        $isValid = $false
    }
    
    # Check cluster connectivity
    try {
        $null = kubectl cluster-info --request-timeout=10s 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Kubernetes cluster is accessible" -ForegroundColor Green
        } else {
            Write-Host "❌ Kubernetes cluster is not accessible" -ForegroundColor Red
            $isValid = $false
        }
    } catch {
        Write-Host "❌ Error connecting to Kubernetes cluster" -ForegroundColor Red
        $isValid = $false
    }
    
    # Check ArgoCD namespace
    try {
        $null = kubectl get namespace argocd 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ ArgoCD namespace exists" -ForegroundColor Green
        } else {
            Write-Host "⚠️  ArgoCD namespace not found" -ForegroundColor Yellow
            Write-Host "   Create it with: kubectl create namespace argocd" -ForegroundColor White
        }
    } catch {
        Write-Host "⚠️  Could not check ArgoCD namespace" -ForegroundColor Yellow
    }
    
    # Check ArgoCD CRDs
    try {
        $null = kubectl get crd applications.argoproj.io 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ ArgoCD CRDs are installed" -ForegroundColor Green
        } else {
            Write-Host "⚠️  ArgoCD CRDs not found" -ForegroundColor Yellow
            Write-Host "   Install ArgoCD first: https://argo-cd.readthedocs.io/en/stable/getting_started/" -ForegroundColor White
        }
    } catch {
        Write-Host "⚠️  Could not check ArgoCD CRDs" -ForegroundColor Yellow
    }
    
    # Check permissions
    try {
        $null = kubectl auth can-i create applications.argoproj.io 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Can create ArgoCD applications" -ForegroundColor Green
        } else {
            Write-Host "⚠️  Cannot create ArgoCD applications" -ForegroundColor Yellow
            Write-Host "   Check RBAC permissions" -ForegroundColor White
        }
    } catch {
        Write-Host "⚠️  Could not check ArgoCD permissions" -ForegroundColor Yellow
    }
    
    if ($isValid) {
        Write-Host "`n🎉 kubectl setup is valid for GitOps automation!" -ForegroundColor Green
    } else {
        Write-Host "`n❌ kubectl setup needs configuration" -ForegroundColor Red
    }
    
    return $isValid
}

# Main execution
Write-Host "🚀 GitOps kubectl Authentication Setup" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green

# Check kubectl availability first
if (-not (Test-KubectlAvailability)) {
    Write-Host "❌ kubectl is not available. Please install kubectl first." -ForegroundColor Red
    Write-Host "   Installation guide: https://kubernetes.io/docs/tasks/tools/install-kubectl/" -ForegroundColor White
    exit 1
}

switch ($Mode) {
    "service-account" {
        $success = New-ServiceAccountSetup -Name $ServiceAccountName -Namespace $Namespace
        if (-not $success) {
            exit 1
        }
    }
    "kubeconfig" {
        $success = New-KubeconfigSetup
        if (-not $success) {
            exit 1
        }
    }
    "validate" {
        $success = Test-KubectlSetup
        if (-not $success) {
            exit 1
        }
    }
}

Write-Host "`n✅ Setup completed successfully!" -ForegroundColor Green
Write-Host "📚 Next steps: See docs/AUTOMATED_DEPLOYMENT_SETUP.md for configuration details" -ForegroundColor Cyan

exit 0
