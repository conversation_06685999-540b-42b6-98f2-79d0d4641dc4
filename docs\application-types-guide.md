# GitOps Application Types Guide

This guide documents the two distinct GitOps deployment templates for React Frontend and Spring Boot Backend applications, along with their complete configuration options and payload structures.

## Overview

The GitOps automation system now supports application type-specific templates that provide optimized configurations for different types of applications:

- **React Frontend Applications** (`react-frontend`)
- **Spring Boot Backend Applications** (`springboot-backend`)
- **Legacy Application Types** (`web-app`, `api`, `microservice`, `worker`, `database`)

## Application Type Configurations

### React Frontend (`react-frontend`)

**Optimized for:**
- Static file serving (Nginx-based)
- Build-time environment variables
- Minimal resource requirements
- No database dependencies (PostgreSQL templates automatically excluded)

**Default Configuration:**
```yaml
Container Port: 3000
Health Check: "/"
Resources:
  - Dev: CPU 50m-200m, Memory 128Mi-256Mi
  - Staging: CPU 100m-500m, Memory 256Mi-512Mi
  - Production: CPU 200m-1000m, Memory 512Mi-1Gi
Database: Disabled (stateless)
Secrets: Minimal (build-time configs only)
```

**Environment Variables:**
```yaml
REACT_APP_API_URL: Backend API endpoint
REACT_APP_ENVIRONMENT: Deployment environment
REACT_APP_VERSION: Application version
REACT_APP_TITLE: Application title
PUBLIC_URL: Public URL path
GENERATE_SOURCEMAP: Source map generation (dev only)
```

### Spring Boot Backend (`springboot-backend`)

**Optimized for:**
- Database integration (PostgreSQL)
- JWT authentication
- Spring Boot Actuator health checks
- Higher resource requirements for JVM

**Default Configuration:**
```yaml
Container Port: 8080
Health Check: "/actuator/health"
Resources:
  - Dev: CPU 250m-500m, Memory 512Mi-1Gi
  - Staging: CPU 500m-1000m, Memory 1Gi-2Gi
  - Production: CPU 1000m-2000m, Memory 2Gi-4Gi
Database: Enabled (PostgreSQL)
Secrets: Full backend secrets (JWT, DB, SMTP, OAuth)
```

**Environment Variables:**
```yaml
# Spring Boot Configuration
SPRING_PROFILES_ACTIVE: Environment profile
SERVER_PORT: Application port
SPRING_APPLICATION_NAME: Application name

# Database Configuration
SPRING_DATASOURCE_URL: PostgreSQL connection string
SPRING_DATASOURCE_USERNAME: Database username (from secret)
SPRING_DATASOURCE_PASSWORD: Database password (from secret)
SPRING_JPA_HIBERNATE_DDL_AUTO: Schema management

# Security & Authentication
JWT_SECRET: JWT signing secret (from secret)
SPRING_SECURITY_OAUTH2_CLIENT_*: OAuth2 configuration

# Mail Configuration
SPRING_MAIL_HOST: SMTP server
SPRING_MAIL_USERNAME: SMTP username (from secret)
SPRING_MAIL_PASSWORD: SMTP password (from secret)

# JVM Configuration
JAVA_OPTS: JVM memory and GC settings
```

## Repository Dispatch Payload Structure

### Minimal Payload (Recommended)

For most deployments, use this minimal payload structure to avoid GitHub Actions limitations:

```json
{
  "app_name": "string (required)",
  "project_id": "string (required, lowercase alphanumeric with hyphens)",
  "application_type": "string (required, enum: react-frontend|springboot-backend|web-app|api|microservice|worker|database)",
  "environment": "string (required, enum: dev|staging|production)",
  "docker_image": "string (required, container registry path)",
  "docker_tag": "string (required, image tag)"
}
```

### Full Payload (Advanced)

For advanced configurations or when you need to override defaults:

```json
{
  "app_name": "string (required)",
  "project_id": "string (required, lowercase alphanumeric with hyphens)",
  "application_type": "string (required)",
  "environment": "string (required)",
  "docker_image": "string (required)",
  "docker_tag": "string (required)",
  "container_port": "number (optional, auto-set based on type)",
  "source_repo": "string (optional, for tracking)",
  "source_branch": "string (optional, for tracking)",
  "commit_sha": "string (optional, for tracking)"
}
```

**Notes**:
- `health_check_path` is automatically set based on `application_type` and cannot be overridden
- GitHub repository dispatch has limitations on payload size
- Use the minimal payload when possible
```

### React Frontend Examples

**Minimal Payload (Recommended):**
```json
{
  "app_name": "My React Dashboard",
  "project_id": "react-dashboard",
  "application_type": "react-frontend",
  "environment": "production",
  "docker_image": "myorg/react-dashboard",
  "docker_tag": "v2.1.0"
}
```

**Full Payload (Advanced):**
```json
{
  "app_name": "My React Dashboard",
  "project_id": "react-dashboard",
  "application_type": "react-frontend",
  "environment": "production",
  "docker_image": "myorg/react-dashboard",
  "docker_tag": "v2.1.0",
  "container_port": 3000,
  "source_repo": "myorg/react-dashboard",
  "source_branch": "main",
  "commit_sha": "abc123def456"
}
```
*Note: `health_check_path` is automatically set to `/` for React frontends*

### Spring Boot Backend Examples

**Minimal Payload (Recommended):**
```json
{
  "app_name": "User Authentication API",
  "project_id": "auth-api",
  "application_type": "springboot-backend",
  "environment": "production",
  "docker_image": "myorg/auth-api",
  "docker_tag": "v1.5.2"
}
```

**Full Payload (Advanced):**
```json
{
  "app_name": "User Authentication API",
  "project_id": "auth-api",
  "application_type": "springboot-backend",
  "environment": "production",
  "docker_image": "myorg/auth-api",
  "docker_tag": "v1.5.2",
  "container_port": 8080,
  "source_repo": "myorg/auth-api",
  "source_branch": "main",
  "commit_sha": "def456ghi789"
}
```
*Note: `health_check_path` is automatically set to `/actuator/health` for Spring Boot backends*

## GitHub Actions Integration

### Triggering from Application Repository

**Minimal Payload (Recommended to avoid GitHub limitations):**
```yaml
name: Deploy to GitOps
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Trigger GitOps Deployment
        uses: peter-evans/repository-dispatch@v2
        with:
          token: ${{ secrets.GITOPS_TOKEN }}
          repository: ChidhagniConsulting/gitops-argocd-apps
          event-type: deploy-to-argocd
          client-payload: |
            {
              "app_name": "My React App",
              "project_id": "my-react-app",
              "application_type": "react-frontend",
              "environment": "production",
              "docker_image": "myorg/my-react-app",
              "docker_tag": "${{ github.sha }}"
            }
```

**Full Payload (Advanced - use only if needed):**
```yaml
          client-payload: |
            {
              "app_name": "My React App",
              "project_id": "my-react-app",
              "application_type": "react-frontend",
              "environment": "production",
              "docker_image": "myorg/my-react-app",
              "docker_tag": "${{ github.sha }}",
              "source_repo": "${{ github.repository }}",
              "source_branch": "${{ github.ref_name }}",
              "commit_sha": "${{ github.sha }}"
            }
```

## Type-Specific Defaults

### Automatic Configuration

The system automatically applies type-specific defaults when certain fields are not provided:

| Field | React Frontend | Spring Boot Backend | Other Types |
|-------|---------------|-------------------|-------------|
| `container_port` | 3000 | 8080 | 8080 |
| `health_check_path` | "/" | "/actuator/health" | "/health" |
| Database | Disabled | Enabled | Disabled |
| Memory (Dev) | 128Mi-256Mi | 512Mi-1Gi | 256Mi-512Mi |
| CPU (Dev) | 50m-200m | 250m-500m | 100m-500m |

### Override Behavior

- If you provide explicit values in the payload, they will override the type-specific defaults
- Resource limits scale automatically based on environment (dev < staging < production)
- Database enablement can be overridden via GitHub Issue template for manual deployments

## Validation Rules

### Required Fields
- `app_name`: Human-readable application name
- `project_id`: Must match pattern `^[a-z0-9-]+$`
- `application_type`: Must be one of the supported types
- `environment`: Must be `dev`, `staging`, or `production`
- `docker_image`: Valid container registry path
- `docker_tag`: Valid image tag

### Optional Fields with Defaults
- `container_port`: Auto-set based on application type
- `health_check_path`: Auto-set based on application type
- `source_repo`, `source_branch`, `commit_sha`: For tracking purposes

## Generated Manifests

Both application types generate the same manifest structure but with type-specific configurations:

```
{project-id}/
├── argocd/
│   ├── application.yaml    # ArgoCD Application with type-specific metadata
│   └── project.yaml        # ArgoCD Project
└── k8s/
    ├── namespace.yaml      # Namespace with app-type label
    ├── deployment.yaml     # Type-specific resources, health checks, env vars
    ├── service.yaml        # Service configuration
    ├── configmap.yaml      # Type-specific environment variables
    ├── secret.yaml         # Type-specific secrets (minimal for React)
    ├── ingress.yaml        # Ingress configuration (if enabled)
    └── postgres-*.yaml     # Database manifests (if enabled)
```

## Security Considerations

### React Frontend
- Minimal secrets (typically none required)
- Build-time environment variables in ConfigMap
- No database credentials needed
- API endpoints configured as environment variables

### Spring Boot Backend
- Full secret management (JWT, database, SMTP, OAuth)
- Runtime environment variables from secrets
- Database integration with credential management
- Comprehensive authentication configuration

## Next Steps

1. **Test Template Generation**: Use the testing guide to validate both application types
2. **Customize Templates**: Modify templates in `templates/` directory for specific needs
3. **Monitor Deployments**: Use ArgoCD UI to monitor application health and sync status
4. **Scale Resources**: Adjust resource limits based on actual usage patterns
