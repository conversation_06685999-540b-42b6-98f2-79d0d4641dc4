#!/usr/bin/env python3
"""
Simple YAML validation script that works with or without PyYAML
"""

import sys
import os
import json


def validate_yaml_basic(file_path):
    """Basic YAML validation without PyYAML"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Basic checks
        if not content.strip():
            print(f"❌ {file_path}: File is empty")
            return False
        
        # Check for basic YAML structure
        lines = content.split('\n')
        has_yaml_content = False
        
        for line in lines:
            line = line.strip()
            if line and not line.startswith('#'):
                has_yaml_content = True
                break
        
        if not has_yaml_content:
            print(f"❌ {file_path}: No YAML content found")
            return False
        
        # Check for common YAML issues
        issues = []
        for i, line in enumerate(lines, 1):
            # Check for tabs (YAML doesn't allow tabs for indentation)
            if '\t' in line:
                issues.append(f"Line {i}: Contains tab character (use spaces)")
            
            # Check for trailing spaces (can cause issues)
            if line.endswith(' ') and line.strip():
                issues.append(f"Line {i}: Has trailing spaces")
        
        if issues:
            print(f"⚠️ {file_path}: Found potential issues:")
            for issue in issues[:5]:  # Show max 5 issues
                print(f"  {issue}")
            if len(issues) > 5:
                print(f"  ... and {len(issues) - 5} more issues")
        
        print(f"✅ {file_path}: Basic validation passed")
        return True
        
    except Exception as e:
        print(f"❌ {file_path}: Error reading file: {e}")
        return False


def validate_yaml_with_pyyaml(file_path):
    """Full YAML validation with PyYAML"""
    try:
        import yaml
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Parse YAML
        try:
            documents = list(yaml.safe_load_all(content))
            if not documents or all(doc is None for doc in documents):
                print(f"❌ {file_path}: No valid YAML documents found")
                return False
            
            print(f"✅ {file_path}: YAML syntax is valid ({len([d for d in documents if d is not None])} documents)")
            return True
            
        except yaml.YAMLError as e:
            print(f"❌ {file_path}: YAML syntax error: {e}")
            return False
            
    except ImportError:
        # Fall back to basic validation
        return validate_yaml_basic(file_path)
    except Exception as e:
        print(f"❌ {file_path}: Validation error: {e}")
        return False


def main():
    if len(sys.argv) < 2:
        print("Usage: python3 validate-yaml.py <file1.yaml> [file2.yaml] ...")
        sys.exit(1)
    
    print("🔍 YAML Validation Starting...")
    
    # Check if PyYAML is available
    try:
        import yaml
        print("✅ PyYAML available - using full validation")
        validate_func = validate_yaml_with_pyyaml
    except ImportError:
        print("⚠️ PyYAML not available - using basic validation")
        validate_func = validate_yaml_basic
    
    all_valid = True
    files_processed = 0
    
    for file_path in sys.argv[1:]:
        if not os.path.exists(file_path):
            print(f"❌ {file_path}: File not found")
            all_valid = False
            continue
        
        if not file_path.lower().endswith(('.yaml', '.yml')):
            print(f"⚠️ {file_path}: Not a YAML file (skipping)")
            continue
        
        files_processed += 1
        if not validate_func(file_path):
            all_valid = False
    
    print(f"\n📊 Validation Summary:")
    print(f"  Files processed: {files_processed}")
    print(f"  Result: {'✅ All files valid' if all_valid else '❌ Some files have issues'}")
    
    if not all_valid:
        print("\n💡 Note: Some validation issues may not prevent deployment")
        print("   Check ArgoCD logs if deployment fails")
    
    # Exit with appropriate code
    sys.exit(0 if all_valid else 1)


if __name__ == "__main__":
    main()
