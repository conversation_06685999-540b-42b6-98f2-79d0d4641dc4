# ARC Integration Guide

This guide provides complete instructions for integrating Actions Runner Controller (ARC) with the GitOps ArgoCD repository for self-hosted GitHub Actions automation.

## Overview

The ARC integration enables:
- Self-hosted GitHub Actions runners on DigitalOcean Kubernetes Service (DOKS)
- Pre-configured kubectl and ArgoCD access
- Automated GitOps deployments without GitHub secrets
- Scalable runner infrastructure with resource optimization

## Prerequisites

- DOKS cluster running with ARC system installed
- kubectl configured for cluster access
- ArgoCD installed in the cluster
- PowerShell 7+ for running scripts

## Quick Start

### 1. Deploy ARC Integration

```powershell
# Run the automated deployment script
.\scripts\deploy-arc-integration.ps1

# Or deploy manually:
kubectl apply -f arc-config/rbac.yaml
kubectl apply -f arc-config/runner-scale-set.yaml
```

### 2. Verify Integration

```powershell
# Run comprehensive tests
.\scripts\test-doks-arc.ps1

# Monitor runner status
.\scripts\monitor-arc-runners.ps1
```

### 3. Test Automation

1. Create a deployment issue using the GitHub issue template
2. Verify the workflow runs on ARC runners
3. Confirm ArgoCD application is created automatically

## Configuration Files

### RBAC Configuration (`arc-config/rbac.yaml`)

- **ServiceAccount**: `gitops-runner-sa` in `ch-arc-runner-system` namespace
- **ClusterRole**: `gitops-runner-role` with comprehensive permissions
- **ClusterRoleBinding**: Links service account to cluster role

**Key Permissions**:
- Full access to ArgoCD CRDs (`applications.argoproj.io`)
- Kubernetes core resources (pods, services, configmaps, secrets)
- Deployment resources (deployments, replicasets, statefulsets)
- Namespace and RBAC management

### Runner Scale Set (`arc-config/runner-scale-set.yaml`)

- **Repository**: `ChidhagniConsulting/gitops-argocd-apps`
- **Labels**: `[self-hosted, Linux]`
- **Replicas**: 1 (auto-scaling 1-5 based on load)
- **Resources**: 512Mi/1Gi memory, 200m/500m CPU

**Features**:
- Container-based runner execution
- kubectl cache volume for performance
- Health checks and probes
- DOKS-optimized resource allocation

## Deployment Commands

### Apply RBAC Configuration

```powershell
# Create namespace if it doesn't exist
kubectl create namespace arc-system

# Apply RBAC manifests
kubectl apply -f arc-config/rbac.yaml

# Verify service account
kubectl get serviceaccount gitops-runner-sa -n arc-system
kubectl get clusterrole gitops-runner-role
kubectl get clusterrolebinding gitops-runner-binding
```

### Deploy Runner Scale Set

```powershell
# Apply runner deployment
kubectl apply -f arc-config/runner-scale-set.yaml

# Verify deployment
kubectl get runnerdeployment gitops-argocd-runner -n arc-system
kubectl get pods -n arc-system -l app=gitops-argocd-runner
```

### Monitor Runner Status

```powershell
# Check runner deployment status
kubectl describe runnerdeployment gitops-argocd-runner -n arc-system

# Monitor pods
kubectl get pods -n arc-system -l app=gitops-argocd-runner -w

# View logs
kubectl logs -n arc-system -l app=gitops-argocd-runner -f

# Check autoscaler
kubectl get hpa gitops-argocd-runner-hpa -n arc-system
```

## Verification Steps

### 1. Test Cluster Connectivity

```powershell
kubectl get nodes
kubectl cluster-info
kubectl get namespaces
```

### 2. Verify ArgoCD CRD Access

```powershell
kubectl get crd applications.argoproj.io
kubectl get applications -n argocd
kubectl auth can-i create applications.argoproj.io --as=system:serviceaccount:arc-system:gitops-runner-sa
```

### 3. Confirm Runner Deployment

```powershell
kubectl get runnerscaleset chidhagni-organisation-runner -n ch-arc-runner-system
kubectl get pods -n ch-arc-runner-system -l app=chidhagni-organisation-runner
kubectl logs -n ch-arc-runner-system -l app=chidhagni-organisation-runner
```

### 4. Test Automated Workflow Execution

1. Create a test deployment issue with the `app-deployment` label
2. Verify workflow runs on ARC runners (check GitHub Actions logs)
3. Confirm ArgoCD application is created automatically
4. Check application sync status in ArgoCD dashboard

## Troubleshooting

### Runner Not Starting

**Symptoms**: No runner pods or pods in CrashLoopBackOff

**Solutions**:
```powershell
# Check runner scale set events
kubectl describe runnerscaleset chidhagni-organisation-runner -n ch-arc-runner-system

# Check pod events and logs
kubectl describe pod <runner-pod> -n arc-system
kubectl logs <runner-pod> -n arc-system

# Verify service account permissions
kubectl auth can-i create applications.argoproj.io --as=system:serviceaccount:arc-system:gitops-runner-sa

# Check resource constraints
kubectl top pods -n arc-system
```

### Workflow Fails on ARC Runner

**Symptoms**: GitHub Actions workflow fails with kubectl or ArgoCD errors

**Solutions**:
```powershell
# Test kubectl access in runner
kubectl exec -n ch-arc-runner-system -it <runner-pod> -- kubectl cluster-info

# Check ArgoCD connectivity
kubectl exec -n ch-arc-runner-system -it <runner-pod> -- kubectl get crd applications.argoproj.io

# Verify runner labels
kubectl get pods -n ch-arc-runner-system -l app=chidhagni-organisation-runner --show-labels

# Check workflow logs in GitHub Actions
```

### GitHub Actions Not Using ARC Runners

**Symptoms**: Workflows still run on GitHub-hosted runners

**Solutions**:
1. Verify runner labels match workflow `runs-on` specification
2. Check if runners are online in GitHub repository settings
3. Ensure ARC controller is running in `ch-arc-runner-system` namespace
4. Verify runner registration with GitHub

```powershell
# Check ARC controller
kubectl get pods -n ch-arc-runner-system -l app.kubernetes.io/name=actions-runner-controller

# Check runner registration
kubectl logs -n ch-arc-runner-system -l app=chidhagni-organisation-runner | grep -i "register"
```

### Resource Issues

**Symptoms**: Pods pending or OOMKilled

**Solutions**:
```powershell
# Check resource usage
kubectl top pods -n ch-arc-runner-system
kubectl describe node

# Adjust resource limits in runner-scale-set.yaml
# Scale down other workloads if needed
kubectl get pods --all-namespaces --sort-by='.status.containerStatuses[0].restartCount'
```

## Security Considerations

- Runners execute with non-root user (UID 1000)
- RBAC follows principle of least privilege
- Network access is controlled through Kubernetes network policies
- Resource limits prevent resource exhaustion
- Docker socket access is read-only where possible

## Performance Optimization

- Runners use local kubectl cache for improved performance
- Auto-scaling based on workflow queue depth
- Resource requests/limits optimized for DOKS
- Ephemeral volumes for runner workspace

## Maintenance

### Updating Runner Image

```powershell
# Edit runner-scale-set.yaml to update image version
kubectl apply -f arc-config/runner-scale-set.yaml

# Force pod recreation
kubectl rollout restart deployment chidhagni-organisation-runner -n ch-arc-runner-system
```

### Scaling Runners

```powershell
# Manual scaling (edit the RunnerScaleSet resource)
kubectl edit runnerscaleset chidhagni-organisation-runner -n ch-arc-runner-system

# Check current scaling status
kubectl get runnerscaleset chidhagni-organisation-runner -n ch-arc-runner-system
```

### Monitoring

```powershell
# Use the monitoring script
.\scripts\monitor-arc-runners.ps1 -Watch

# Check metrics
kubectl top pods -n ch-arc-runner-system
kubectl get events -n ch-arc-runner-system --sort-by='.lastTimestamp'
```

## Support

For issues with ARC integration:

1. Run the test suite: `.\scripts\test-doks-arc.ps1`
2. Check the monitoring dashboard: `.\scripts\monitor-arc-runners.ps1`
3. Review GitHub Actions workflow logs
4. Check ArgoCD application status
5. Consult the troubleshooting section above
