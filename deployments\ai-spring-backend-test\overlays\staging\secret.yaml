apiVersion: v1
kind: Secret
metadata:
  name: ai-spring-backend-test-secrets
  labels:
    app: ai-spring-backend-test
    app.kubernetes.io/name: ai-spring-backend-test
    app.kubernetes.io/component: secrets
    app.kubernetes.io/part-of: ai-spring-backend-test
    app.kubernetes.io/version: "9a327539"
    app.kubernetes.io/managed-by: argocd
    environment: staging
type: Opaque
data:
  # Staging Environment Secrets
  # These placeholders will be replaced with base64-encoded values from secrets_encoded
  SPRING_DATASOURCE_URL: ************************************************************************************************************************************************************************
  
  # Essential Authentication Secrets
  JWT_SECRET: dmVyeWxvbmdjaGlkaGdhbmlsb2NhbHNlY3JldHlvdXdpbGxub3R1bmRlcnN0YW5kZGFoc2RzamFsa2Rqc2FkbnNhZHNhbGtkanNhbGtkaGFjeWRzYWhmZGxrZmpkc2xrZmpkc2Zwb2RzaWZwZHN5Y3hpb3Zsa2N4dmZkZmpkc2hmaXVzeWZ1eXJlcmV3anJld2Zkc2Fkc2Fkc2Fkc2Fkc2FkYXNkc2Fkc2Fkc2FkY3h2Y3h2ZGY=
  
  # Database Credentials (Staging)
  DB_USER: c3ByaW5nX3N0YWdpbmdfdXNlcg==
  DB_PASSWORD: QVZOU19IRVYxQWpZVXNZS19sYWFHdG1w
  DB_HOST: cHJpdmF0ZS1kYmFhcy1kYi0xMDMyOTMyOC1kby11c2VyLTIzODE1NzQyLTAubS5kYi5vbmRpZ2l0YWxvY2Vhbi5jb20=
  DB_PORT: MjUwNjA=
  DB_NAME: c3ByaW5nX3N0YWdpbmdfZGI=
  DB_SSL_MODE: cmVxdWlyZQ==
  
  # SMTP Configuration (Staging)
  SMTP_USER: ****************************************
  SMTP_PASS: ZnFhY3RlaGFmbXpsbHR6eg==
  
  # OAuth2 Configuration (Staging)
  GOOGLE_CLIENT_ID: MTA3Mzk4MTg2NDUzOC0zdWlpazcyb2hzZnIyb3Vpb3JvcjNmbTFqcWM0OTNvcy5hcHBzLmdvb2dsZXVzZXJjb250ZW50LmNvbQ==
  GOOGLE_CLIENT_SECRET: R09DU1BYLTcyRjBONEg5aGlMSVk1U3o1Z3pCczI5OEFBYlQ=
  
  # Staging-specific secrets
  # MONITORING_API_KEY: DYNAMIC_MONITORING_API_KEY_B64
  # EXTERNAL_API_KEY: DYNAMIC_EXTERNAL_API_KEY_B64
