# GitOps ArgoCD Applications

This repository contains ArgoCD applications and Kubernetes manifests for GitOps deployments, featuring a **self-service automation system** for deploying applications through GitHub Issues.

## 🚀 Application Deployment Options

This repository supports two deployment methods:

### 1. Self-Service Issue-Based Deployment

Deploy applications manually by creating a GitHub Issue! Perfect for testing, hotfixes, and manual deployments.

**Quick Start:**

1. **[Create Deployment Issue](../../issues/new/choose)** → Select "🚀 Application Deployment Request"
2. **Fill the form** with your application details
3. **Submit** → Automation generates manifests AND deploys to cluster automatically!
4. **Monitor** your live application in ArgoCD dashboard

### 2. Automated CI/CD Integration

Automatically deploy applications when code is merged to main branch! Perfect for continuous deployment workflows.

**Quick Start:**

1. **Add GitOps integration** to your application's CI/CD pipeline
2. **Merge feature branch** to main branch
3. **Automatic deployment** → CI/CD triggers GitOps workflow automatically!
4. **Monitor** your live application in ArgoCD dashboard

📖 **[View CI/CD Integration Guide](docs/application-cicd-integration.md)**

> 🚀 **Auto-deployment enabled!** Applications are automatically deployed to your minikube cluster via ARC runners.

### Features

- ✅ **Complete automation** - From issue to live deployment in one step
- ✅ **Auto-deployment** - Automatically deploys ArgoCD applications to cluster
- ✅ **ARC Integration** - Uses self-hosted runners with kubectl access
- ✅ **Application Types** - React Frontend, Spring Boot Backend, Web apps, APIs, microservices
- ✅ **Optional components** - PostgreSQL, Ingress, Persistent Volumes
- ✅ **Environment support** - Dev, staging, production configurations
- ✅ **CI/CD Integration** - Automatic deployments from application repositories
- ✅ **Validation** - Comprehensive input validation with helpful errors
- ✅ **Best practices** - Following Kubernetes and ArgoCD standards

📖 **[Complete Documentation](docs/SELF_SERVICE_DEPLOYMENT.md)** | 🧪 **[Examples](examples/)** | 🔧 **[Testing Guide](tests/test-scenarios.md)** | 🏃 **[ARC Integration](#-arc-integration)**

## 🎯 Application Types

The GitOps automation system supports optimized templates for different application types:

### React Frontend (`react-frontend`)
- **Optimized for**: Static file serving, build-time configs, minimal resources
- **Default Port**: 3000
- **Health Check**: `/`
- **Resources**: Lower CPU/memory requirements
- **Database**: Disabled (stateless)
- **Backend Support**: Configurable backend connectivity via `backend_type` parameter (spring, django, nest)
- **Use Case**: React, Vue, Angular applications

### Spring Boot Backend (`springboot-backend`)
- **Optimized for**: Database integration, JWT auth, Spring Boot Actuator
- **Default Port**: 8080
- **Health Check**: `/actuator/health`
- **Resources**: Higher CPU/memory for JVM
- **Database**: Enabled (PostgreSQL)
- **Use Case**: Spring Boot APIs, microservices

### Legacy Types
- `web-app`, `api`, `microservice`, `worker`, `database` - General purpose templates

📖 **[Application Types Guide](docs/application-types-guide.md)** | 🧪 **[Testing Guide](docs/testing-application-types.md)** | ⚡ **[Payload Optimization](docs/payload-optimization-guide.md)**

## 🔗 Backend Type Configuration

The AI React Frontend supports dynamic backend connectivity through the `backend_type` parameter:

| Backend Type | Service | Dev Environment URL | Port |
|--------------|---------|-------------------|------|
| `spring` | AI Spring Backend | `http://64.225.85.162:8080` | 8080 |
| `django` | AI Django Backend | `http://144.126.253.213:8000` | 8000 |
| `nest` | AI NestJS Backend | `http://64.225.85.162:3000` | 3000 |

### Usage Example
```json
{
  "event_type": "deploy-to-argocd",
  "client_payload": {
    "app_name": "AI React Frontend",
    "project_id": "ai-react-frontend",
    "application_type": "react-frontend",
    "environment": "dev",
    "docker_image": "registry.digitalocean.com/doks-registry/ai-react-frontend",
    "docker_tag": "latest",
    "backend_type": "django"
  }
}
```

📖 **[Backend Type Configuration Guide](docs/backend-type-configuration.md)**

## Applications

### AI Nest Backend
- **Path**: `ai-nest-backend/`
- **Application Repository**: https://github.com/ChidhagniConsulting/ai-nest-backend
- **Deployment**: NestJS Authentication API with PostgreSQL

## Manual Deployment

```powershell
# Deploy ArgoCD application
kubectl apply -f ai-nest-backend/argocd/project.yaml
kubectl apply -f ai-nest-backend/argocd/application.yaml

# Or use the deployment script
.\ai-nest-backend\scripts\deploy-argocd.ps1

## 🏃 ARC Integration

This repository is integrated with **Actions Runner Controller (ARC)** for self-hosted GitHub Actions runners on DigitalOcean Kubernetes Service (DOKS). The automation workflows run on dedicated ARC runners with pre-configured kubectl and ArgoCD access.

### ARC Runner Configuration

- **Namespace**: `ch-arc-runner-system`
- **Runner Labels**: `[self-hosted, Linux]`
- **Repository**: `ChidhagniConsulting/gitops-argocd-apps`
- **Service Account**: `gitops-runner-sa` with cluster-wide permissions

### Deployment Commands

```powershell
# 1. Apply RBAC configuration
kubectl apply -f arc-config/rbac.yaml

# 2. Deploy runner scale set
kubectl apply -f arc-config/runner-scale-set.yaml

# 3. Verify ARC integration
.\scripts\test-doks-arc.ps1

# 4. Enable auto-deployment
.\scripts\enable-auto-deploy.ps1

# 5. Monitor runner status
kubectl get runnerdeployment -n arc-system
kubectl get pods -n arc-system -l app=gitops-argocd-runner
```

### Auto-Deployment Configuration

To enable automatic ArgoCD deployment:

1. **Set Repository Variable**:
   - Go to GitHub repository Settings → Secrets and variables → Actions → Variables
   - Create variable: `ENABLE_AUTO_DEPLOY` = `true`

2. **Verify Setup**:
   ```powershell
   .\scripts\enable-auto-deploy.ps1 -Verify
   ```

3. **Test Auto-Deployment**:
   - Create a deployment issue using the GitHub template
   - Workflow will automatically deploy to ArgoCD after generating manifests
   - Monitor progress in GitHub Actions and ArgoCD dashboard

### Verification Steps

1. **Test cluster connectivity from ARC runners**:
   ```powershell
   kubectl get nodes
   kubectl cluster-info
   ```

2. **Verify ArgoCD CRD access**:
   ```powershell
   kubectl get crd applications.argoproj.io
   kubectl get applications -n argocd
   ```

3. **Confirm runner deployment**:
   ```powershell
   kubectl get runnerdeployment gitops-argocd-runner -n arc-system
   kubectl logs -n arc-system -l app=gitops-argocd-runner
   ```

4. **Test automated workflow execution**:
   - Create a test deployment issue
   - Verify it runs on ARC runners (check workflow logs)
   - Confirm ArgoCD application is created automatically

### Troubleshooting

#### Runner Not Starting
```powershell
# Check runner deployment status
kubectl describe runnerdeployment gitops-argocd-runner -n arc-system

# Check pod logs
kubectl logs -n arc-system -l app=gitops-argocd-runner

# Verify service account permissions
kubectl auth can-i create applications.argoproj.io --as=system:serviceaccount:arc-system:gitops-runner-sa
```

#### Workflow Fails on ARC Runner
```powershell
# Check runner pod resources
kubectl top pods -n arc-system

# Verify kubectl access in runner
kubectl exec -n arc-system -it <runner-pod> -- kubectl cluster-info

# Check ArgoCD connectivity
kubectl exec -n arc-system -it <runner-pod> -- kubectl get crd applications.argoproj.io
```

#### GitHub Actions Not Using ARC Runners
- Verify runner labels match workflow `runs-on` specification
- Check if runners are online in GitHub repository settings
- Ensure ARC controller is running in `arc-system` namespace

### Resource Limits

The ARC runners are optimized for minikube with conservative resource limits:
- **Memory**: 256Mi request, 512Mi limit
- **CPU**: 100m request, 250m limit
- **Replicas**: 1-3 (auto-scaling based on load)

### Security Features

- **Non-root execution**: Runners run as user 1000
- **RBAC**: Comprehensive cluster role with minimal required permissions
- **Network policies**: Isolated runner network access
- **Resource quotas**: Prevents resource exhaustion in minikube